'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Crown, 
  UserCheck, 
  User, 
  Search, 
  Mail, 
  Calendar,
  Loader2,
  Check,
  X,
  Shield,
  AlertTriangle,
  Send
} from "lucide-react"
import { cn } from "@/lib/utils"

interface User {
  id: string
  email: string
  role: 'USER' | 'HR' | 'ADMIN'
  created_at: string
  last_sign_in_at: string | null
}

interface ConfirmAction {
  type: 'role-change' | 'password-reset'
  userId: string
  userEmail: string
  newRole?: 'USER' | 'HR' | 'ADMIN'
}

const roleConfig = {
  USER: {
    icon: User,
    label: 'User',
    color: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800',
    description: 'Basic user access'
  },
  HR: {
    icon: UserCheck,
    label: 'HR Manager',
    color: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800',
    description: 'HR management access'
  },
  ADMIN: {
    icon: Crown,
    label: 'Administrator',
    color: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800',
    description: 'Full system access'
  }
}

export default function RoleUpdatePage() {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)
  const [updatingUsers, setUpdatingUsers] = useState<Set<string>>(new Set())
  const [sendingResetEmails, setSendingResetEmails] = useState<Set<string>>(new Set())
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const [confirmAction, setConfirmAction] = useState<ConfirmAction | null>(null)

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers()
  }, [])

  // Filter users based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = users.filter(user =>
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredUsers(filtered)
    } else {
      setFilteredUsers(users)
    }
  }, [searchTerm, users])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/auth/update-role', {
        method: 'GET',
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch users')
      }
      
      setUsers(data.users)
      setFilteredUsers(data.users)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  const updateUserRole = async (userId: string, newRole: 'USER' | 'HR' | 'ADMIN') => {
    try {
      setUpdatingUsers(prev => new Set(prev).add(userId))
      setError('')
      setSuccessMessage('')
      
      const response = await fetch('/api/auth/update-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, role: newRole }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update role')
      }
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId ? { ...user, role: newRole } : user
      ))
      
      const user = users.find(u => u.id === userId)
      setSuccessMessage(`Successfully updated ${user?.email} to ${newRole} role`)
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update role')
    } finally {
      setUpdatingUsers(prev => {
        const newSet = new Set(prev)
        newSet.delete(userId)
        return newSet
      })
      setConfirmAction(null)
    }
  }

  const sendPasswordResetEmail = async (email: string, userId: string) => {
    try {
      setSendingResetEmails(prev => new Set(prev).add(userId))
      setError('')
      setSuccessMessage('')
      
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'send-reset-email', 
          email 
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send reset email')
      }
      
      setSuccessMessage(`Password reset email sent to ${email}`)
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send reset email')
    } finally {
      setSendingResetEmails(prev => {
        const newSet = new Set(prev)
        newSet.delete(userId)
        return newSet
      })
      setConfirmAction(null)
    }
  }

  const handleRoleChangeRequest = (userId: string, newRole: 'USER' | 'HR' | 'ADMIN') => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setConfirmAction({
        type: 'role-change',
        userId,
        userEmail: user.email,
        newRole
      })
    }
  }

  const handlePasswordResetRequest = (userId: string) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setConfirmAction({
        type: 'password-reset',
        userId,
        userEmail: user.email
      })
    }
  }

  const confirmActionHandler = () => {
    if (!confirmAction) return

    if (confirmAction.type === 'role-change' && confirmAction.newRole) {
      updateUserRole(confirmAction.userId, confirmAction.newRole)
    } else if (confirmAction.type === 'password-reset') {
      sendPasswordResetEmail(confirmAction.userEmail, confirmAction.userId)
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getRoleStats = () => {
    const stats = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return [
      { role: 'USER', count: stats.USER || 0 },
      { role: 'HR', count: stats.HR || 0 },
      { role: 'ADMIN', count: stats.ADMIN || 0 },
    ]
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <Loader2 className="w-12 h-12 animate-spin mx-auto text-blue-600" />
              <p className="text-muted-foreground">Loading users...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950">
      {/* Confirmation Modal */}
      {confirmAction && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {confirmAction.type === 'role-change' ? (
                  <>
                    <Shield className="w-5 h-5" />
                    Confirm Role Change
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    Confirm Password Reset
                  </>
                )}
              </CardTitle>
              <CardDescription>
                {confirmAction.type === 'role-change' 
                  ? `Are you sure you want to change ${confirmAction.userEmail}'s role to ${confirmAction.newRole}?`
                  : `Are you sure you want to send a password reset email to ${confirmAction.userEmail}?`
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setConfirmAction(null)}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={confirmActionHandler}
                disabled={updatingUsers.has(confirmAction.userId) || sendingResetEmails.has(confirmAction.userId)}
              >
                {(updatingUsers.has(confirmAction.userId) || sendingResetEmails.has(confirmAction.userId)) ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : null}
                Confirm
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-3">
              <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            User Role Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Manage user roles and permissions across your application. Update roles to control access levels.
          </p>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="max-w-4xl mx-auto mb-6">
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 flex items-start gap-3 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300">
              <AlertTriangle className="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Error:</strong> {error}
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="max-w-4xl mx-auto mb-6">
            <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-start gap-3 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300">
              <Check className="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Success:</strong> {successMessage}
              </div>
            </div>
          </div>
        )}

        <div className="max-w-6xl mx-auto space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {getRoleStats().map(({ role, count }) => {
              const config = roleConfig[role as keyof typeof roleConfig]
              const Icon = config.icon
              return (
                <Card key={role} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {config.label}s
                        </p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                          {count}
                        </p>
                      </div>
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-3">
                        <Icon className="w-6 h-6 text-gray-600 dark:text-gray-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Search and Users Table */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    All Users ({filteredUsers.length})
                  </CardTitle>
                  <CardDescription>
                    Manage user roles and permissions
                  </CardDescription>
                </div>
                <Button onClick={fetchUsers} variant="outline" size="sm">
                  <Loader2 className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
              
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search users by email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardHeader>

            <CardContent className="p-0">
              <div className="overflow-hidden">
                {filteredUsers.length === 0 ? (
                  <div className="text-center py-12">
                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">
                      {searchTerm ? 'No users found matching your search.' : 'No users found.'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-0">
                    {filteredUsers.map((user) => {
                      const isUpdating = updatingUsers.has(user.id)
                      const isSendingReset = sendingResetEmails.has(user.id)
                      
                      return (
                        <div
                          key={user.id}
                          className={cn(
                            "p-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0",
                            "hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                          )}
                        >
                          <div className="flex items-center justify-between flex-wrap gap-4">
                            <div className="flex items-center space-x-4 min-w-0 flex-1">
                              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-2 flex-shrink-0">
                                <Mail className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                                  {user.email}
                                </h3>
                                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1 flex-wrap">
                                  <span className="flex items-center gap-1 whitespace-nowrap">
                                    <Calendar className="w-3 h-3 flex-shrink-0" />
                                    Joined {formatDate(user.created_at)}
                                  </span>
                                  <span className="whitespace-nowrap">
                                    Last login: {formatDate(user.last_sign_in_at)}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-3 flex-shrink-0">
                              {/* Current Role Badge */}
                              <Badge 
                                variant="outline" 
                                className={roleConfig[user.role].color}
                              >
                                {React.createElement(roleConfig[user.role].icon, { 
                                  className: "w-3 h-3 mr-1" 
                                })}
                                <span className="hidden sm:inline">{roleConfig[user.role].label}</span>
                                <span className="sm:hidden">{roleConfig[user.role].label.split(' ')[0]}</span>
                              </Badge>

                              {/* Actions Container */}
                              <div className="flex items-center space-x-2">
                                {/* Password Reset Button */}
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handlePasswordResetRequest(user.id)}
                                  disabled={isSendingReset}
                                  className="text-orange-600 border-orange-200 hover:bg-orange-50 dark:text-orange-400 dark:border-orange-800 dark:hover:bg-orange-900/20"
                                  title="Send password reset email"
                                >
                                  {isSendingReset ? (
                                    <Loader2 className="w-3 h-3 animate-spin" />
                                  ) : (
                                    <Send className="w-3 h-3" />
                                  )}
                                  <span className="ml-1 hidden sm:inline">Reset</span>
                                </Button>

                                {/* Role Update Buttons */}
                                <div className="flex space-x-1">
                                  {(['USER', 'HR', 'ADMIN'] as const).map((role) => {
                                    const config = roleConfig[role]
                                    const Icon = config.icon
                                    const isCurrent = user.role === role
                                    
                                    return (
                                      <Button
                                        key={role}
                                        size="sm"
                                        variant={isCurrent ? "default" : "outline"}
                                        onClick={() => handleRoleChangeRequest(user.id, role)}
                                        disabled={isCurrent || isUpdating}
                                        className={cn(
                                          "min-w-[60px] text-xs",
                                          isCurrent && "pointer-events-none opacity-75"
                                        )}
                                        title={`Set role to ${config.label}`}
                                      >
                                        {isUpdating ? (
                                          <Loader2 className="w-3 h-3 animate-spin" />
                                        ) : (
                                          <>
                                            <Icon className="w-3 h-3 sm:mr-1" />
                                            <span className="hidden sm:inline">{config.label.split(' ')[0]}</span>
                                          </>
                                        )}
                                      </Button>
                                    )
                                  })}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}