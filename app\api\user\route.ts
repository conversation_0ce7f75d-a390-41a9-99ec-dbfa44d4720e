import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { authenticateUser, canAccessUsers } from "@/lib/auth"

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check permissions - only ADMIN can access user list
    if (!canAccessUsers(authResult.user, 'READ')) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        employeeId: true,
        role: true,
        createdAt: true,
        updatedAt: true
        // Exclude password field for security
      }
    })

    if (!users || users.length === 0) {
      return NextResponse.json({ message: "No users found" }, { status: 404 })
    }

    return NextResponse.json(users)
  } catch (error) {
    console.error("[GET USERS ERROR]", error)
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}