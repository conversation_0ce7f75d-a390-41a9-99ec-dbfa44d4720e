import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const users = await prisma.user.findMany()

    if (!users || users.length === 0) {
      return NextResponse.json({ message: "No users found" }, { status: 404 })
    }

    return NextResponse.json(users)
  } catch (error) {
    console.error("[GET USERS ERROR]", error)
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}