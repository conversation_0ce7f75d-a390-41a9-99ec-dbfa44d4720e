"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { useRouter } from "next/navigation"
import { LogOut, Loader2 } from "lucide-react"
import { useState } from "react"

type SignOutButtonProps = {
  label?: string
  redirectPath?: string
  variant?: "default" | "outline" | "destructive" | "ghost" | "secondary"
  className?: string
  showIcon?: boolean
  size?: "sm" | "default" | "lg"
}

export function SignOutButton({
  label = "Sign Out",
  redirectPath = "/",
  variant = "outline",
  className,
  showIcon = true,
  size = "default",
}: SignOutButtonProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSignOut = async () => {
    setIsLoading(true)
    
    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error("Error signing out:", error.message)
        setIsLoading(false)
        return
      }

      // Clear all Supabase cookies manually
      const allCookies = document.cookie.split(';')
      allCookies.forEach(cookie => {
        const eqPos = cookie.indexOf('=')
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim()
        if (name.startsWith('sb-')) {
          // Clear the cookie for current domain and path
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
          // Also try without domain for localhost
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`
        }
      })

      // Clear localStorage and sessionStorage
      localStorage.clear()
      sessionStorage.clear()

      // Force a hard redirect to ensure all state is cleared
      window.location.replace(redirectPath)
      
    } catch (error) {
      console.error("Sign out error:", error)
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleSignOut}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          Signing out...
        </>
      ) : (
        <>
          {showIcon && <LogOut className="w-4 h-4 mr-2" />}
          {label}
        </>
      )}
    </Button>
  )
}