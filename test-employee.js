// Simple script to create a test employee using Prisma
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const testEmployee = {
  employeeId: "TEST001",
  employeeName: "Test Employee",
  email: "<EMAIL>",
  isActive: true,
  dob: new Date("1990-01-01"),
  presentAddress: "Test Address",
  permanentAddress: "Test Address",
  mobileNumber: "**********",
  aadhar: "**********12",
  pan: "**********",
  bankName: "SBI",
  accountNo: "**********",
  ifsc: "SBIN0001234",
  branch: "Test Branch",
  bloodGroup: "O+",
  maritalStatus: "Single",
  department: "IT",
  doj: new Date("2024-01-01"),
  educations: {
    create: [
      {
        instituteName: "Test University",
        qualification: "B.Tech"
      }
    ]
  }
};

async function createTestEmployee() {
  try {
    const result = await prisma.employee.create({
      data: testEmployee,
      include: {
        educations: true,
        workExperiences: true
      }
    });

    console.log('Test employee created:', result);
    console.log('Employee ID:', result.id);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestEmployee();
