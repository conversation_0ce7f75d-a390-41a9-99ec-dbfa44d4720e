import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const { filePath } = await request.json();
    
    if (!filePath) {
      return NextResponse.json({ error: "File path required" }, { status: 400 });
    }

    // Extract the file path from the full URL
    const pathMatch = filePath.match(/\/object\/public\/ctq-files\/(.+)$/);
    const cleanPath = pathMatch ? pathMatch[1] : filePath;

    // Generate new signed URL (1 hour expiry)
    const { data: signedUrlData, error } = await supabaseAdmin.storage
      .from('ctq-files')
      .createSignedUrl(cleanPath, 3600);

    if (error) {
      console.error('Signed URL error:', error);
      return NextResponse.json({ error: "Failed to generate signed URL" }, { status: 500 });
    }

    return NextResponse.json({ signedUrl: signedUrlData.signedUrl });
  } catch (error) {
    console.error('Refresh URL error:', error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}