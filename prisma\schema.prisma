// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init


generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
        

model Employee {
  id                 String   @id @default(cuid())
  employeeId         String   @unique
  employeeName       String
  email              String?  @unique
  isActive           Boolean  @default(true)
  
  @@index([isActive])
  @@index([employeeName])
  dob                DateTime
  presentAddress     String?
  permanentAddress   String?
  mobileNumber       String
  aadhar             String   @unique
  pan                String   @unique
  bankName           String?
  accountNo          String?
  ifsc               String?
  branch             String?
  bloodGroup         String?
  maritalStatus      String?
  department         String?
  doj                DateTime
  previousOrganization String?
  previousExperience String?
  previousESI        String?
  previousUAN        String?
  fatherName         String?
  fatherAadhar       String?
  motherName         String?
  motherAadhar       String?

  // File URLs from Supabase storage
  profilePicUrl       String?
  expCertificateUrl   String?
  panCopyUrl          String?
  aadharCopyUrl       String?
  bankCopyUrl         String?
  fatherAadharCopyUrl String?
  motherAadharCopyUrl String?

  // Relations
  educations Education[]
  workExperiences WorkExperience[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  attendances Attendance[]
  salaries    Salary[]
  payslips    Payslip[]
}

model WorkExperience {
  id                   String   @id @default(cuid())
  previousOrganization String
  previousExperience   String
  previousESI          String?
  previousUAN          String?
  experienceCertUrl    String?
  employeeId           String
  employee             Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model Education {
  id            String   @id @default(cuid())
  instituteName String
  qualification String
  certificateUrl String?
  employeeId    String
  employee      Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model User {
  id         Int      @id @default(autoincrement())
  email      String   @unique
  name       String
  password   String
  employeeId String   @unique
  role       Role     @default(EMPLOYEE)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  files      File[]
}

model File {
  id           Int      @id @default(autoincrement())
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  storageUrl   String // Supabase Storage URL
  userId       Int
  fileType     String // 'profile_pic' | 'document'
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Attendance {
  id           String   @id @default(cuid())
  employeeId   String
  month        DateTime // First day of the month (e.g., 2025-09-01)
  workingDays  Int      // Total working days in month
  daysPresent  Int      // Days employee was present
  isLocked     Boolean  @default(false) // Prevent edits after payslip
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  employee     Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  
  @@unique([employeeId, month])
  @@index([month])
  @@index([employeeId])
}

model Salary {
  id             String   @id @default(cuid())
  employeeId     String
  effectiveFrom  DateTime // Validity start date
  basic          Decimal  @db.Decimal(10, 2)
  hra            Decimal  @db.Decimal(10, 2)
  otherAllowance Decimal  @db.Decimal(10, 2)
  deductions     Decimal  @db.Decimal(10, 2)
  pf             Decimal  @db.Decimal(10, 2)
  esi            Decimal  @db.Decimal(10, 2)
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  employee       Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  
  @@unique([employeeId, effectiveFrom])
  @@index([isActive])
  @@index([effectiveFrom])
  @@index([employeeId])
}

model Payslip {
  id             String   @id @default(cuid())
  employeeId     String
  month          DateTime // First day of the month (e.g., 2025-09-01)
  workingDays    Int      @default(30)
  daysPresent    Int      @default(30)
  basic          Decimal  @db.Decimal(10, 2)
  hra            Decimal  @db.Decimal(10, 2)
  otherAllowance Decimal  @db.Decimal(10, 2)
  grossSalary    Decimal  @db.Decimal(10, 2)
  deductions     Decimal  @db.Decimal(10, 2)
  netSalary      Decimal  @db.Decimal(10, 2)
  generatedAt    DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  employee       Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  
  @@unique([employeeId, month])
  @@index([month])
  @@index([employeeId])
}

enum Role {
  ADMIN
  EMPLOYEE
  HR
}

