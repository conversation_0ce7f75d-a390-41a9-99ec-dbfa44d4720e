# API Security Implementation

This document outlines the comprehensive security measures implemented to protect all API endpoints in the application.

## Overview

All API endpoints are now secured with authentication and role-based authorization. No API can be accessed without proper authentication, and each endpoint enforces appropriate permissions based on user roles.

## Security Architecture

### 1. Authentication Middleware (`lib/auth.ts`)

The authentication system provides:
- **Session Validation**: Validates Supabase sessions for all API requests
- **User Information Extraction**: Extracts user details and roles from session
- **Role-Based Authorization**: Enforces permissions based on user roles
- **Utility Functions**: Helper functions for common permission checks

### 2. Role-Based Permissions

The system defines four user roles with hierarchical permissions:

#### ADMIN
- Full access to all resources
- Can manage users, employees, and files
- Can delete employees and access all data

#### HR
- Can manage employees (create, read, update)
- Can access all employee data
- Can manage files
- Cannot delete employees or manage users

#### EMPLOYEE
- Can read employee data
- Can manage their own files
- Limited access to system features

#### USER
- Basic access level
- Can manage their own files only
- Cannot access employee management features

### 3. Protected API Endpoints

#### Employee Management APIs
- **GET /api/employees** - Requires: HR, ADMIN, EMPLOYEE roles
- **POST /api/employees** - Requires: HR, ADMIN roles
- **GET /api/employees/[id]** - Requires: HR, ADMIN, EMPLOYEE roles
- **PUT /api/employees/[id]** - Requires: HR, ADMIN roles
- **DELETE /api/employees/[id]** - Requires: ADMIN role only

#### File Management APIs
- **POST /api/files/upload** - Requires: Any authenticated user
- **GET /api/files/[userId]** - Requires: File ownership or HR/ADMIN role
- **DELETE /api/files/delete** - Requires: File ownership or HR/ADMIN role

#### User Management APIs
- **GET /api/user** - Requires: ADMIN role only

#### Utility APIs
- **POST /api/refresh-urls** - Requires: EMPLOYEE, HR, or ADMIN role

### 4. Middleware Protection

The Next.js middleware (`middleware.ts`) provides:
- **Route Protection**: Protects both page routes and API routes
- **Session Validation**: Validates sessions at the middleware level
- **API Authentication**: Returns 401 for unauthenticated API requests
- **Auth Endpoint Exclusion**: Allows access to authentication endpoints

## Implementation Details

### Authentication Flow

1. **Request Interception**: Middleware intercepts all requests to protected routes
2. **Session Validation**: Validates Supabase session using cookies (Next.js 15 compatible)
3. **API Route Handling**: For API routes, returns 401 if no session exists
4. **Individual Route Authorization**: Each API route performs role-based checks
5. **Response**: Returns appropriate error codes for unauthorized access

### Next.js 15 Compatibility

The authentication system is fully compatible with Next.js 15:
- Uses `createRouteHandlerClient({ cookies })` for proper async cookie handling
- Handles the new async cookies API requirements
- Maintains session validation across all protected routes

### Permission Checking

Each protected API endpoint follows this pattern:

```typescript
export async function GET(request: NextRequest) {
  // 1. Authenticate user
  const authResult = await authenticateUser(request);
  if (!authResult.success || !authResult.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // 2. Check permissions
  if (!canAccessEmployees(authResult.user, 'READ')) {
    return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
  }

  // 3. Process request
  // ... business logic
}
```

### Error Responses

The system returns standardized error responses:
- **401 Unauthorized**: No valid session or authentication failed
- **403 Forbidden**: Valid session but insufficient permissions
- **400 Bad Request**: Invalid input or validation errors
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side errors

## Security Features

### 1. Session-Based Authentication
- Uses Supabase authentication with secure session management
- Sessions are validated on every API request
- Automatic session refresh handled by Supabase

### 2. Role-Based Access Control (RBAC)
- Hierarchical role system with clear permission boundaries
- Fine-grained permissions for different operations
- Role validation on every protected endpoint

### 3. Data Protection
- User passwords excluded from API responses
- File access restricted by ownership
- Employee data access controlled by role

### 4. Input Validation
- All API inputs validated using Zod schemas
- Proper error handling for invalid data
- SQL injection prevention through Prisma ORM

## Testing Security

To test the security implementation:

1. **Unauthenticated Access**: Try accessing APIs without authentication
   - Should return 401 Unauthorized

2. **Insufficient Permissions**: Try accessing APIs with lower-privilege roles
   - Should return 403 Forbidden

3. **Valid Access**: Access APIs with appropriate roles
   - Should return expected data

4. **File Ownership**: Try accessing files owned by other users
   - Should return 403 Forbidden (unless admin/HR)

## Excluded Endpoints

The following endpoints remain publicly accessible:
- **POST /api/auth/login** - User login
- **POST /api/auth/signup** - User registration
- **POST /api/auth/reset-password** - Password reset (send email action)
- **POST /api/auth/signup/signup-temp** - Temporary signup

## Maintenance

### Adding New Protected Endpoints

1. Import authentication utilities:
   ```typescript
   import { authenticateUser, canPerformAction } from '@/lib/auth';
   ```

2. Add authentication check at the beginning of the handler
3. Add appropriate permission checks based on the resource
4. Update middleware matcher if needed

### Modifying Permissions

1. Update the `PERMISSIONS` object in `lib/auth.ts`
2. Modify role checking functions as needed
3. Test all affected endpoints

## Security Best Practices Implemented

- ✅ Authentication required for all sensitive APIs
- ✅ Role-based authorization with least privilege principle
- ✅ Input validation and sanitization
- ✅ Secure session management
- ✅ Proper error handling without information leakage
- ✅ File access control with ownership validation
- ✅ Password exclusion from API responses
- ✅ Middleware-level protection for defense in depth
