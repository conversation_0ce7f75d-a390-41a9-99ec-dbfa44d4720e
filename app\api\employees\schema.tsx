import * as z from "zod";

// Education schema
export const educationSchema = z.object({
  instituteName: z.string(),
  qualification: z.string(),
  certificateUrl: z.string().optional(),
});

// Work Experience schema
export const workExperienceSchema = z.object({
  previousOrganization: z.string(),
  previousExperience: z.string(),
  previousESI: z.string().optional().refine(val => !val || /^\d{10}$/.test(val), "ESI must be 10 digits"),
  previousUAN: z.string().optional().refine(val => !val || /^\d{12}$/.test(val), "UAN must be 12 digits"),
  experienceCertUrl: z.string().optional(),
});

// Zod schema for request validation
export const employeeSchema = z.object({
  employeeId: z.string(),
  employeeName: z.string(),
  email: z.string().email("Invalid email format").optional(),
  isActive: z.boolean(),
  dob: z.string().refine(val => {
    const date = new Date(val);
    const today = new Date();
    const age = today.getFullYear() - date.getFullYear();
    return age >= 18 && age <= 65;
  }, "Age must be between 18 and 65 years"),
  presentAddress: z.string().optional(),
  permanentAddress: z.string().optional(),
  mobileNumber: z.string().regex(/^[6-9]\d{9}$/, "Invalid mobile number"),
  aadhar: z.string().regex(/^(\d{4}\s?\d{4}\s?\d{4}|\d{12})$/, "Invalid Aadhar format"),
  pan: z.string().regex(/^[A-Z]{5}\d{4}[A-Z]$/, "Invalid PAN format"),
  bankName: z.string().optional(),
  accountNo: z.string().optional(),
  ifsc: z.string().optional(),
  branch: z.string().optional(),
  bloodGroup: z.string().optional(),
  department: z.string().optional(),
  educations: z.array(educationSchema).min(1),
  workExperiences: z.array(workExperienceSchema).optional(),
  maritalStatus: z.string().optional(),
  doj: z.string().refine(val => {
    const dojDate = new Date(val);
    const today = new Date();
    return dojDate <= today;
  }, "Date of joining cannot be in the future"),
  previousOrganization: z.string().optional(),
  previousExperience: z.string().optional(),
  previousESI: z.string().optional(),
  previousUAN: z.string().optional(),
  fatherName: z.string().optional(),
  fatherAadhar: z.string().optional(),
  motherName: z.string().optional(),
  motherAadhar: z.string().optional(),
  // File URLs (already uploaded to Supabase Storage)
  profilePicUrl: z.string().optional(),

  expCertificateUrl: z.string().optional(),
  panCopyUrl: z.string().optional(),
  aadharCopyUrl: z.string().optional(),
  bankCopyUrl: z.string().optional(),
  fatherAadharCopyUrl: z.string().optional(),
  motherAadharCopyUrl: z.string().optional(),
});


export type Employee = z.infer<typeof employeeSchema>;