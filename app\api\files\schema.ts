import { z } from 'zod';

export const fileUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, 'File size must be less than 10MB')
    .refine(
      (file) => ['image/jpeg', 'image/png', 'image/webp', 'application/pdf', 'text/plain'].includes(file.type),
      'File type must be JPEG, PNG, WebP, PDF, or TXT'
    ),
  fileType: z.enum(['profile_pic', 'document']),
  userId: z.number().min(1),
});

export const fileDeleteSchema = z.object({
  fileId: z.number().min(1),
  userId: z.number().min(1),
});