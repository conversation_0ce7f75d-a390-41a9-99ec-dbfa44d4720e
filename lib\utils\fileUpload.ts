import { supabaseAdmin } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

export async function uploadToSupabase(
  file: File,
  bucket: string,
  folder: string = ''
): Promise<{ url: string; path: string }> {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client is not available. Check SUPABASE_SERVICE_ROLE_KEY environment variable.');
  }

  const fileExt = file.name.split('.').pop();
  const fileName = `${uuidv4()}.${fileExt}`;
  const filePath = folder ? `${folder}/${fileName}` : fileName;

  const { data, error } = await supabaseAdmin.storage
    .from(bucket)
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false,
    });

  if (error) {
    console.error('Supabase upload error:', error);
    throw new Error(`Upload failed: ${error.message}`);
  }

  console.log('Upload successful:', data);

  // Get public URL for the uploaded file
  const { data: publicUrlData } = supabaseAdmin.storage
    .from(bucket)
    .getPublicUrl(filePath);

  console.log('Generated public URL:', publicUrlData.publicUrl);

  return {
    url: publicUrlData.publicUrl,
    path: filePath,
  };
}

export async function deleteFromSupabase(
  bucket: string,
  filePath: string
): Promise<void> {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client is not available. Check SUPABASE_SERVICE_ROLE_KEY environment variable.');
  }

  const { error } = await supabaseAdmin.storage
    .from(bucket)
    .remove([filePath]);

  if (error) {
    throw new Error(`Delete failed: ${error.message}`);
  }
}