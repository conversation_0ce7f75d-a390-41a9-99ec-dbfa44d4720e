import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  // This is important - refresh the session
  const { data: { session }, error } = await supabase.auth.getSession()

  const pathname = req.nextUrl.pathname
  // console.log("pathname", pathname)
  // console.log("session exists:", !!session)
  // console.log("session user:", session?.user?.email)
  // console.log("auth error:", error)
  // console.log("cookies:", req.cookies.getAll().map(c => c.name))

  // Not logged in → redirect to login for protected routes
  if (!session && (pathname.startsWith("/user") || pathname.startsWith("/admin") || pathname.startsWith("/hr"))) {
    const loginUrl = req.nextUrl.clone()
    loginUrl.pathname = "/"
    return NextResponse.redirect(loginUrl)
  }

  // If logged in, get role from app_metadata or user_metadata, default to USER
  const role = session?.user?.app_metadata?.role || session?.user?.user_metadata?.role || 'USER'

  // Role-based access control
  if (pathname.startsWith("/admin") && role !== "ADMIN") {
    return NextResponse.redirect(new URL("/unauthorized", req.url))
  }

  if (pathname.startsWith("/hr") && role !== "HR" && role !== "ADMIN") {
    return NextResponse.redirect(new URL("/unauthorized", req.url))
  }

  if (pathname.startsWith("/user") && !["USER","ADMIN","HR"].includes(role)) {
    return NextResponse.redirect(new URL("/unauthorized", req.url))
  }

  return res
}

export const config = {
  matcher: ["/user/:path*", "/admin/:path*", "/hr/:path*"]
}