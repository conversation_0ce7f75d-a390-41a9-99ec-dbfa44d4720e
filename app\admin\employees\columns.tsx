"use client"

import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, Hash, Eye, Edit, Trash2 } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { SortableHeader } from "@/lib/table-utils"
type EmployeeListItem = {
  id: string;
  employeeId: string;
  employeeName: string;
  email: string | null;
  isActive: boolean;
  department: string | null;
  mobileNumber: string;
  doj: Date;
  createdAt: Date;
  updatedAt: Date;
  profilePicUrl: string | null;
}

// Helper function to get user initials
function getInitials(name: string) {
  return name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2)
}

export const employeeColumns: ColumnDef<EmployeeListItem>[] = [
  
  // Employee info (avatar + name)
  {
    accessorKey: "employeeName",
    header: ({ column }) => <SortableHeader column={column} title="Employee" />,
    cell: ({ row }) => {
      const employee = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage 
              src={employee.profilePicUrl || undefined} 
              alt={employee.employeeName} 
              className="object-cover"
            />
            <AvatarFallback>{getInitials(employee.employeeName)}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-medium">{employee.employeeName}</span>
            <span className="text-sm text-muted-foreground">
              {employee.email || 'No email'}
            </span>
            <span className="text-xs text-muted-foreground">
              {employee.mobileNumber}
            </span>
          </div>
        </div>
      )
    },
  },

  // Employee ID
  {
    accessorKey: "employeeId",
    header: ({ column }) => <SortableHeader column={column} title="Employee ID" />,
    cell: ({ row }) => (
      <div className="font-mono text-sm flex items-center">
        <Hash className="mr-1 h-3 w-3 text-muted-foreground" />
        {row.getValue("employeeId")}
      </div>
    ),
  },

  // Department/Position
  {
    accessorKey: "department",
    header: ({ column }) => <SortableHeader column={column} title="Department" />,
    cell: ({ row }) => {
      const department = row.getValue("department") as string
      const getDepartmentColor = (dept: string) => {
        switch (dept) {
          case "IT": return "bg-blue-100 text-blue-800 border-blue-200"
          case "HR": return "bg-green-100 text-green-800 border-green-200"
          case "Finance": return "bg-yellow-100 text-yellow-800 border-yellow-200"
          case "Marketing": return "bg-purple-100 text-purple-800 border-purple-200"
          case "Operations": return "bg-orange-100 text-orange-800 border-orange-200"
          case "Sales": return "bg-red-100 text-red-800 border-red-200"
          default: return "bg-gray-100 text-gray-800 border-gray-200"
        }
      }
      return department ? (
        <Badge variant="outline" className={getDepartmentColor(department)}>
          {department}
        </Badge>
      ) : <span className="text-muted-foreground">-</span>
    },
  },

{
  accessorKey: "doj",
  header: ({ column }) => <SortableHeader column={column} title="Date of Joining" />,
  cell: ({ row }) => {
    const doj = row.getValue("doj") as string;

    const formattedDate = doj
      ? new Intl.DateTimeFormat("en-US", {
          year: "numeric",
          month: "short",
          day: "2-digit",
        }).format(new Date(doj))
      : null;

    const calculateExperience = (dojDate: string) => {
      const startDate = new Date(dojDate);
      const currentDate = new Date();
      const diffTime = Math.abs(currentDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      const years = Math.floor(diffDays / 365);
      const months = Math.floor((diffDays % 365) / 30);
      
      if (years > 0) {
        return `${years}y ${months}m`;
      } else {
        return `${months}m`;
      }
    };

    const experience = doj ? calculateExperience(doj) : null;

    return formattedDate ? (
      <div className="flex flex-col">
        <span className="text-sm">{formattedDate}</span>
        <span className="text-xs text-muted-foreground">{experience}</span>
      </div>
    ) : (
      <span className="text-muted-foreground">-</span>
    );
  },
}
,

  // Active Status
  {
    accessorKey: "isActive",
    header: ({ column }) => <SortableHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const isActive = row.getValue("isActive") as boolean
      return (
        <Badge variant={isActive ? "default" : "secondary"} className={isActive ? "bg-green-100 text-green-800 border-green-200" : "bg-red-100 text-red-800 border-red-200"}>
          {isActive ? "Active" : "Inactive"}
        </Badge>
      )
    },
  },

  // Actions
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const employee = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(employee.mobileNumber)}
            >
              Copy mobile number
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(employee.employeeId)}
            >
              Copy employee ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <Link href={`/admin/employees/${employee.id}`}>
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                View profile
              </DropdownMenuItem>
            </Link>
            <Link href={`/admin/employees/${employee.id}/edit`}>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit employee
              </DropdownMenuItem>
            </Link>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => {
                if (confirm('Are you sure you want to delete this employee?')) {
                  fetch(`/api/employees/${employee.id}`, {
                    method: 'DELETE'
                  }).then(() => {
                    window.location.reload();
                  });
                }
              }}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete employee
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]