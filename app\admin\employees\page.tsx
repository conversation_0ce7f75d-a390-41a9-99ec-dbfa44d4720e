'use client'

import { useState, useEffect } from 'react'
import { employeeColumns } from "./columns"
import { ReusableDataTable } from "@/components/data-table"

export default function EmployeesPage() {
  const [employees, setEmployees] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees')
      const data = await response.json()
      setEmployees(data)
    } catch (error) {
      console.error('Error fetching employees:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div>Loading...</div>

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-hidden">
        <ReusableDataTable
          columns={employeeColumns}
          data={employees}
          searchKey="employeeName"
          searchPlaceholder="Search employees..."
          showColumnToggle={true}
          showPagination={true}
          showSearch={true}
          pageSize={10}
          createLink="/admin/employees/create"
          createButtonLabel="Add Employee"
        />
      </div>
    </div>
  )
}