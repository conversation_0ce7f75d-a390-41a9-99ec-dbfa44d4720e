import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { User } from '@supabase/supabase-js'

export interface AuthenticatedUser {
  id: string
  email: string
  role: 'ADMIN' | 'HR' | 'EMPLOYEE' | 'USER'
  supabaseUser: User
}

export interface AuthResult {
  success: boolean
  user?: AuthenticatedUser
  error?: string
}

/**
 * Authenticate user from request and return user information
 */
export async function authenticateUser(_request: NextRequest): Promise<AuthResult> {
  try {
    const cookieStore = waitcookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get session from Supabase
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      return { success: false, error: 'Authentication error' }
    }
    
    if (!session || !session.user) {
      return { success: false, error: 'No active session' }
    }
    
    // Extract role from user metadata
    const role = session.user.app_metadata?.role || session.user.user_metadata?.role || 'USER'
    
    // Validate role
    const validRoles = ['ADMIN', 'HR', 'EMPLOYEE', 'USER']
    if (!validRoles.includes(role)) {
      return { success: false, error: 'Invalid user role' }
    }
    
    const authenticatedUser: AuthenticatedUser = {
      id: session.user.id,
      email: session.user.email || '',
      role: role as AuthenticatedUser['role'],
      supabaseUser: session.user
    }
    
    return { success: true, user: authenticatedUser }
  } catch (error) {
    console.error('Authentication error:', error)
    return { success: false, error: 'Authentication failed' }
  }
}

/**
 * Check if user has required role
 */
export function hasRole(user: AuthenticatedUser, requiredRoles: string[]): boolean {
  return requiredRoles.includes(user.role)
}

/**
 * Check if user has admin privileges
 */
export function isAdmin(user: AuthenticatedUser): boolean {
  return user.role === 'ADMIN'
}

/**
 * Check if user has HR privileges
 */
export function isHR(user: AuthenticatedUser): boolean {
  return user.role === 'HR' || user.role === 'ADMIN'
}

/**
 * Check if user has employee privileges
 */
export function isEmployee(user: AuthenticatedUser): boolean {
  return ['EMPLOYEE', 'HR', 'ADMIN'].includes(user.role)
}

/**
 * Middleware wrapper for API routes that require authentication
 */
export function withAuth(
  handler: (request: NextRequest, context: unknown, user: AuthenticatedUser) => Promise<NextResponse>,
  requiredRoles?: string[]
) {
  return async (request: NextRequest, context: unknown) => {
    const authResult = await authenticateUser(request)
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Check role requirements
    if (requiredRoles && !hasRole(authResult.user, requiredRoles)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }
    
    return handler(request, context, authResult.user)
  }
}

/**
 * Create unauthorized response
 */
export function unauthorizedResponse(message: string = 'Unauthorized') {
  return NextResponse.json({ error: message }, { status: 401 })
}

/**
 * Create forbidden response
 */
export function forbiddenResponse(message: string = 'Forbidden') {
  return NextResponse.json({ error: message }, { status: 403 })
}

/**
 * Permission definitions for different resources
 */
export const PERMISSIONS = {
  EMPLOYEES: {
    READ: ['ADMIN', 'HR', 'EMPLOYEE'],
    CREATE: ['ADMIN', 'HR'],
    UPDATE: ['ADMIN', 'HR'],
    DELETE: ['ADMIN']
  },
  USERS: {
    READ: ['ADMIN'],
    CREATE: ['ADMIN'],
    UPDATE: ['ADMIN'],
    DELETE: ['ADMIN']
  },
  FILES: {
    READ: ['ADMIN', 'HR', 'EMPLOYEE', 'USER'],
    UPLOAD: ['ADMIN', 'HR', 'EMPLOYEE', 'USER'],
    DELETE: ['ADMIN', 'HR', 'EMPLOYEE', 'USER'] // Users can only delete their own files
  },
  ROLES: {
    UPDATE: ['ADMIN']
  }
} as const

/**
 * Check if user can perform action on resource
 */
export function canPerformAction(
  user: AuthenticatedUser,
  resource: keyof typeof PERMISSIONS,
  action: string
): boolean {
  const resourcePermissions = PERMISSIONS[resource] as Record<string, readonly string[]>
  const allowedRoles = resourcePermissions[action]

  if (!allowedRoles) {
    return false
  }

  return allowedRoles.includes(user.role)
}

/**
 * Check if user can access employee data
 */
export function canAccessEmployees(user: AuthenticatedUser, action: 'READ' | 'CREATE' | 'UPDATE' | 'DELETE'): boolean {
  return canPerformAction(user, 'EMPLOYEES', action)
}

/**
 * Check if user can access user management
 */
export function canAccessUsers(user: AuthenticatedUser, action: 'READ' | 'CREATE' | 'UPDATE' | 'DELETE'): boolean {
  return canPerformAction(user, 'USERS', action)
}

/**
 * Check if user can access files (with ownership check for non-admin users)
 */
export function canAccessFiles(user: AuthenticatedUser, action: 'READ' | 'UPLOAD' | 'DELETE', fileOwnerId?: string): boolean {
  const hasPermission = canPerformAction(user, 'FILES', action)

  if (!hasPermission) {
    return false
  }

  // Admin and HR can access all files
  if (user.role === 'ADMIN' || user.role === 'HR') {
    return true
  }

  // For other users, check ownership if fileOwnerId is provided
  if (fileOwnerId && user.id !== fileOwnerId) {
    return false
  }

  return true
}

/**
 * Validate API key or token (for future API key authentication)
 */
export function validateApiKey(_apiKey: string): boolean {
  // Placeholder for API key validation
  // This can be implemented later for service-to-service authentication
  return false
}
