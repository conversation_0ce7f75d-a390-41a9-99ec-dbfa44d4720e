// app/api/auth/reset-password/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// Admin client for password reset
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // Service role key
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const { action, email, password, userId } = await request.json()

    if (action === 'send-reset-email') {
      // Public endpoint - send password reset email
      const supabase = createRouteHandlerClient({ cookies })
      
      if (!email) {
        return NextResponse.json({ error: 'Email is required' }, { status: 400 })
      }

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`
      })

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 400 })
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Password reset email sent successfully' 
      })

    } else if (action === 'admin-reset-password') {
      // Admin only - reset user password directly
      const supabase = createRouteHandlerClient({ cookies })
      
      // Check if user is authenticated and has admin role
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      const currentUserRole = session.user.app_metadata?.role || session.user.user_metadata?.role
      if (currentUserRole !== 'ADMIN') {
        return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
      }

      if (!userId || !password) {
        return NextResponse.json({ error: 'Missing userId or password' }, { status: 400 })
      }

      // Validate password strength
      if (password.length < 6) {
        return NextResponse.json({ error: 'Password must be at least 6 characters' }, { status: 400 })
      }

      // Update user password using admin client
      const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
        password: password
      })

      if (error) {
        console.error('Password reset error:', error)
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Password reset successfully',
        user: { id: data.user.id, email: data.user.email }
      })

    } else if (action === 'update-password') {
      // User updates their own password
      const supabase = createRouteHandlerClient({ cookies })
      
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      if (!password) {
        return NextResponse.json({ error: 'Password is required' }, { status: 400 })
      }

      // Validate password strength
      if (password.length < 6) {
        return NextResponse.json({ error: 'Password must be at least 6 characters' }, { status: 400 })
      }

      const { error } = await supabase.auth.updateUser({ password })

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 400 })
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Password updated successfully' 
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}