import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, Trash2, ArrowLeft, Calendar, MapPin, Phone, Mail, User, GraduationCap, Briefcase, CreditCard, FileText, Heart, Users } from "lucide-react"
import Link from "next/link"
import prisma from "@/lib/prisma"

async function getEmployee(id: string) {
  try {
    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        educations: true,
        workExperiences: true,
      }
    });
    
    if (!employee) {
      throw new Error('Employee not found');
    }
    
    return employee;
  } catch (error) {
    console.error('Error fetching employee:', error);
    throw new Error('Failed to fetch employee');
  }
}

export default async function ViewEmployeePage({ params }: { params: { id: string } }) {
  const { id } = await params
  const employee = await getEmployee(id);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <Link href="/admin/employees">
            <Button variant="ghost" className="hover:bg-white/50 transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Employees
            </Button>
          </Link>
          <div className="flex gap-3">
            <Link href={`/admin/employees/${employee.id}/edit`}>
              <Button variant="outline" className="bg-white/80 hover:bg-white shadow-sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            </Link>
            <Button variant="destructive" className="shadow-sm">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Hero Card - Personal Information */}
        <Card className="overflow-hidden bg-white/80 backdrop-blur-sm shadow-xl border-0">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 h-32 relative">
            <div className="absolute inset-0 bg-black/10"></div>
          </div>
          <CardContent className="relative pt-0 pb-8">
            <div className="flex flex-col sm:flex-row items-start gap-6 -mt-16 relative z-10">
              <Avatar className="h-32 w-32 border-4 border-white shadow-xl">
                <AvatarImage 
                  src={employee.profilePicUrl || undefined} 
                  alt={employee.employeeName} 
                  className="object-cover" 
                />
                <AvatarFallback className="text-2xl bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                  {employee.employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 pt-16 sm:pt-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{employee.employeeName}</h1>
                    <div className="flex items-center gap-2 text-gray-600 mb-3">
                      <User className="h-4 w-4" />
                      <span className="font-medium">ID: {employee.employeeId}</span>
                      <span className="text-gray-400">•</span>
                      <span>{employee.department || 'Department N/A'}</span>
                    </div>
                  </div>
                  <Badge 
                    variant={employee.isActive ? "default" : "secondary"} 
                    className={`${employee.isActive 
                      ? "bg-emerald-100 text-emerald-700 border-emerald-200 hover:bg-emerald-100" 
                      : "bg-red-100 text-red-700 border-red-200 hover:bg-red-100"
                    } px-3 py-1 text-sm font-medium`}
                  >
                    {employee.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-50 border border-blue-100">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Email</p>
                      <p className="text-sm text-gray-900 truncate">{employee.email || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50 border border-green-100">
                    <Phone className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Mobile</p>
                      <p className="text-sm text-gray-900">{employee.mobileNumber}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-purple-50 border border-purple-100">
                    <Calendar className="h-5 w-5 text-purple-600" />
                    <div>
                      <p className="text-xs font-medium text-purple-600 uppercase tracking-wide">Joined</p>
                      <p className="text-sm text-gray-900">{new Date(employee.doj).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-orange-50 border border-orange-100">
                    <Heart className="h-5 w-5 text-orange-600" />
                    <div>
                      <p className="text-xs font-medium text-orange-600 uppercase tracking-wide">Blood Group</p>
                      <p className="text-sm text-gray-900 font-medium">{employee.bloodGroup || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Details */}
            <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <User className="h-5 w-5 text-blue-600" />
                  Personal Details
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Date of Birth</label>
                    <p className="text-gray-900 font-medium">{new Date(employee.dob).toLocaleDateString()}</p>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Marital Status</label>
                    <p className="text-gray-900 font-medium">{employee.maritalStatus || 'N/A'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  Address Information
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Present Address</label>
                    <div className="p-3 bg-gray-50 rounded-lg border">
                      <p className="text-gray-900">{employee.presentAddress || 'N/A'}</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Permanent Address</label>
                    <div className="p-3 bg-gray-50 rounded-lg border">
                      <p className="text-gray-900">{employee.permanentAddress || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Education */}
            {employee.educations && employee.educations.length > 0 && (
              <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                <CardHeader className="border-b border-gray-100">
                  <CardTitle className="flex items-center gap-2 text-gray-900">
                    <GraduationCap className="h-5 w-5 text-blue-600" />
                    Education
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {employee.educations.map((edu, index) => (
                      <div key={edu.id} className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                        <div className="flex justify-between items-start mb-3">
                          <Badge variant="outline" className="bg-white/80">
                            Education {index + 1}
                          </Badge>
                          {edu.certificateUrl && (
                            <a 
                              href={edu.certificateUrl} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-blue-600 hover:text-blue-700 text-sm font-medium hover:underline flex items-center gap-1"
                            >
                              <FileText className="h-4 w-4" />
                              View Certificate
                            </a>
                          )}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-xs font-semibold text-blue-700 uppercase tracking-wide">Institute</label>
                            <p className="text-gray-900 font-medium">{edu.instituteName}</p>
                          </div>
                          <div>
                            <label className="text-xs font-semibold text-blue-700 uppercase tracking-wide">Qualification</label>
                            <p className="text-gray-900 font-medium">{edu.qualification}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Work Experience */}
            {employee.workExperiences && employee.workExperiences.length > 0 && (
              <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                <CardHeader className="border-b border-gray-100">
                  <CardTitle className="flex items-center gap-2 text-gray-900">
                    <Briefcase className="h-5 w-5 text-blue-600" />
                    Work Experience
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {employee.workExperiences.map((exp, index) => (
                      <div key={exp.id} className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                        <div className="flex justify-between items-start mb-3">
                          <Badge variant="outline" className="bg-white/80">
                            Experience {index + 1}
                          </Badge>
                          {exp.experienceCertUrl && (
                            <a 
                              href={exp.experienceCertUrl} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-green-600 hover:text-green-700 text-sm font-medium hover:underline flex items-center gap-1"
                            >
                              <FileText className="h-4 w-4" />
                              View Certificate
                            </a>
                          )}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-xs font-semibold text-green-700 uppercase tracking-wide">Organization</label>
                            <p className="text-gray-900 font-medium">{exp.previousOrganization}</p>
                          </div>
                          <div>
                            <label className="text-xs font-semibold text-green-700 uppercase tracking-wide">Experience</label>
                            <p className="text-gray-900 font-medium">{exp.previousExperience}</p>
                          </div>
                          <div>
                            <label className="text-xs font-semibold text-green-700 uppercase tracking-wide">Previous ESI</label>
                            <p className="text-gray-900 font-medium">{exp.previousESI || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-xs font-semibold text-green-700 uppercase tracking-wide">Previous UAN</label>
                            <p className="text-gray-900 font-medium">{exp.previousUAN || 'N/A'}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Family Details */}
            {(employee.fatherName || employee.motherName) && (
              <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
                <CardHeader className="border-b border-gray-100">
                  <CardTitle className="flex items-center gap-2 text-gray-900">
                    <Users className="h-5 w-5 text-blue-600" />
                    Family Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6 space-y-4">
                  <div>
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Father Name</label>
                    <p className="text-gray-900 font-medium mt-1">{employee.fatherName || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Mother Name</label>
                    <p className="text-gray-900 font-medium mt-1">{employee.motherName || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Documents */}
            <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6 space-y-4">
                <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex justify-between items-start mb-2">
                    <label className="text-xs font-semibold text-yellow-700 uppercase tracking-wide">Aadhar Number</label>
                    {employee.aadharCopyUrl && (
                      <a href={employee.aadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-yellow-600 hover:underline text-xs">
                        View Copy
                      </a>
                    )}
                  </div>
                  <p className="text-gray-900 font-mono">{employee.aadhar}</p>
                </div>

                <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                  <div className="flex justify-between items-start mb-2">
                    <label className="text-xs font-semibold text-indigo-700 uppercase tracking-wide">PAN Number</label>
                    {employee.panCopyUrl && (
                      <a href={employee.panCopyUrl} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:underline text-xs">
                        View Copy
                      </a>
                    )}
                  </div>
                  <p className="text-gray-900 font-mono">{employee.pan}</p>
                </div>

                {employee.fatherAadhar && (
                  <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start mb-2">
                      <label className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Father Aadhar</label>
                      {employee.fatherAadharCopyUrl && (
                        <a href={employee.fatherAadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:underline text-xs">
                          View Copy
                        </a>
                      )}
                    </div>
                    <p className="text-gray-900 font-mono">{employee.fatherAadhar}</p>
                  </div>
                )}

                {employee.motherAadhar && (
                  <div className="p-3 bg-rose-50 rounded-lg border border-rose-200">
                    <div className="flex justify-between items-start mb-2">
                      <label className="text-xs font-semibold text-rose-700 uppercase tracking-wide">Mother Aadhar</label>
                      {employee.motherAadharCopyUrl && (
                        <a href={employee.motherAadharCopyUrl} target="_blank" rel="noopener noreferrer" className="text-rose-600 hover:underline text-xs">
                          View Copy
                        </a>
                      )}
                    </div>
                    <p className="text-gray-900 font-mono">{employee.motherAadhar}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
              <CardHeader className="border-b border-gray-100">
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                  Bank Details
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6 space-y-4">
                <div>
                  <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Bank Name</label>
                  <p className="text-gray-900 font-medium mt-1">{employee.bankName || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Account Number</label>
                  <p className="text-gray-900 font-mono mt-1">{employee.accountNo || 'N/A'}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">IFSC Code</label>
                    <p className="text-gray-900 font-mono mt-1">{employee.ifsc || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Branch</label>
                    <p className="text-gray-900 font-medium mt-1">{employee.branch || 'N/A'}</p>
                  </div>
                </div>
                {employee.bankCopyUrl && (
                  <div className="pt-2">
                    <a 
                      href={employee.bankCopyUrl} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm font-medium hover:underline"
                    >
                      <FileText className="h-4 w-4" />
                      View Bank Document
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}