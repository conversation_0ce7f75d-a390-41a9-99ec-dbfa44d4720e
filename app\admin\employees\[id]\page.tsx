import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, Trash2, ArrowLeft, Calendar, MapPin, Phone, Mail, User, GraduationCap, Briefcase, CreditCard, FileText, Heart, Users, Download, Eye, Building2, Clock, Award } from "lucide-react"
import Link from "next/link"
import prisma from "@/lib/prisma"

async function getEmployee(id: string) {
  try {
    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        educations: true,
        workExperiences: true,
      }
    });
    
    if (!employee) {
      throw new Error('Employee not found');
    }
    
    return employee;
  } catch (error) {
    console.error('Error fetching employee:', error);
    throw new Error('Failed to fetch employee');
  }
}

export default async function ViewEmployeePage({ params }: { params: { id: string } }) {
  const { id } = await params
  const employee = await getEmployee(id);

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-3xl -translate-x-48 -translate-y-48"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-indigo-200/20 to-pink-200/20 rounded-full blur-3xl translate-x-48 translate-y-48"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto p-4 space-y-6">
        {/* Enhanced Header Section */}
        <div className="flex items-center justify-between backdrop-blur-sm bg-white/60 rounded-xl p-3 shadow-sm border border-white/20">
          <Link href="/admin/employees">
            <Button variant="ghost" className="hover:bg-white/70 transition-all duration-300 rounded-lg group">
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Employees
            </Button>
          </Link>
          <div className="flex gap-2">
            <Link href={`/admin/employees/${employee.id}/edit`}>
              <Button variant="outline" className="bg-white/80 hover:bg-white shadow-sm border-0 rounded-lg transition-all duration-300 hover:scale-105">
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            </Link>
            <Link href={`/api/employees/${employee.id}`}>
            <Button variant="destructive" className="shadow-sm rounded-lg transition-all duration-300 hover:scale-105">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
            </Link>
          </div>
        </div>

        {/* Enhanced Hero Card */}
        <Card className="pt-0 overflow-hidden bg-white/80 backdrop-blur-md shadow-lg border-0 rounded-2xl pt-0">
          <div className="relative h-32">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            <div className="absolute top-3 right-4 flex gap-1">
              <div className="w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-white/40 rounded-full animate-pulse delay-100"></div>
              <div className="w-2 h-2 bg-white/50 rounded-full animate-pulse delay-200"></div>
            </div>
          </div>
          <CardContent className="relative pt-0 pb-5">
            <div className="flex flex-col lg:flex-row items-start gap-6 -mt-16 relative z-20">
              <div className="relative">
                <Avatar className="h-28 w-28 border-4 border-white shadow-lg ring-2 ring-white/50 -mt-4">
                  <AvatarImage 
                    src={employee.profilePicUrl || undefined} 
                    alt={employee.employeeName} 
                    className="object-cover" 
                  />
                  <AvatarFallback className="text-2xl bg-gradient-to-br from-blue-500 to-purple-500 text-white font-bold">
                    {employee.employeeName.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-1 -right-2">
                  <Badge 
                    variant={employee.isActive ? "default" : "secondary"} 
                    className={`${employee.isActive 
                      ? "bg-emerald-500 text-white border-emerald-400 shadow-sm" 
                      : "bg-red-500 text-white border-red-400 shadow-sm"
                    } px-2 py-0.5 text-xs font-semibold rounded-full`}
                  >
                    {employee.isActive ? "ACTIVE" : "INACTIVE"}
                  </Badge>
                </div>
              </div>
              
              <div className="flex-1 pt-12 lg:pt-0">
                <div className="flex flex-col lg:flex-row lg:items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-white mb-3 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text">
                      {employee.employeeName}
                    </h1>
                    <div className="flex items-center gap-2 text-gray-600 mb-3">
                      <div className="flex items-center gap-2 bg-blue-50 px-2 py-1 rounded-lg">
                        <User className="h-3 w-3 text-blue-600" />
                        <span className="font-medium text-blue-900 text-sm">ID: {employee.employeeId}</span>
                      </div>
                      {employee.department && (
                        <div className="flex items-center gap-2 bg-purple-50 px-2 py-1 rounded-lg">
                          <Building2 className="h-3 w-3 text-purple-600" />
                          <span className="font-medium text-purple-900 text-sm">{employee.department}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Enhanced Info Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3">
                  <div className="group bg-gradient-to-br from-blue-50 to-blue-100/50 p-3 rounded-xl border border-blue-200/50 hover:shadow-md hover:shadow-blue-500/10 transition-all duration-300">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-blue-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                        <Mail className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-bold text-blue-700 uppercase tracking-wide">Email</p>
                        <p className="text-sm text-gray-900 truncate font-medium">{employee.email || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="group bg-gradient-to-br from-emerald-50 to-emerald-100/50 p-3 rounded-xl border border-emerald-200/50 hover:shadow-md hover:shadow-emerald-500/10 transition-all duration-300">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-emerald-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                        <Phone className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-bold text-emerald-700 uppercase tracking-wide">Mobile</p>
                        <p className="text-sm text-gray-900 font-medium">{employee.mobileNumber}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="group bg-gradient-to-br from-violet-50 to-violet-100/50 p-3 rounded-xl border border-violet-200/50 hover:shadow-md hover:shadow-violet-500/10 transition-all duration-300">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-violet-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                        <Calendar className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-bold text-violet-700 uppercase tracking-wide">Joined</p>
                        <p className="text-sm text-gray-900 font-medium">{new Date(employee.doj).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="group bg-gradient-to-br from-rose-50 to-rose-100/50 p-3 rounded-xl border border-rose-200/50 hover:shadow-md hover:shadow-rose-500/10 transition-all duration-300">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-rose-500 rounded-lg group-hover:scale-110 transition-transform duration-300">
                        <Heart className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-bold text-rose-700 uppercase tracking-wide">Blood Group</p>
                        <p className="text-sm text-gray-900 font-bold">{employee.bloodGroup || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Main Content */}
          <div className="xl:col-span-2 space-y-6">
            {/* Personal Details */}
            <Card className="bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500 p-0 gap-0">
              <CardHeader className="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-gray-100/50 p-4 gap-0">
                <CardTitle className="flex items-center gap-2 text-lg text-gray-900">
                  <div className="p-1.5 bg-blue-500 rounded-lg">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  Personal Details
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="group">
                    <label className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-2 block">Date of Birth</label>
                    <div className="flex items-center gap-2 p-2.5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <p className="text-gray-900 font-semibold">{new Date(employee.dob).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div className="group">
                    <label className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-2 block">Marital Status</label>
                    <div className="flex items-center gap-2 p-2.5 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                      <Heart className="h-4 w-4 text-purple-600" />
                      <p className="text-gray-900 font-semibold">{employee.maritalStatus || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
              <CardHeader className="gap-0 bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-gray-100/50 p-4">
                <CardTitle className="flex items-center gap-2 text-lg text-gray-900">
                  <div className="p-1.5 bg-emerald-500 rounded-lg">
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                  Address Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-2 block">Present Address</label>
                    <div className="p-3 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border border-gray-200 min-h-[60px] flex items-center">
                      <p className="text-gray-900 leading-relaxed text-sm">{employee.presentAddress || 'N/A'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-xs font-bold text-gray-500 uppercase tracking-wide mb-2 block">Permanent Address</label>
                    <div className="p-3 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border border-gray-200 min-h-[60px] flex items-center">
                      <p className="text-gray-900 leading-relaxed text-sm">{employee.permanentAddress || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Education */}
            {employee.educations && employee.educations.length > 0 && (
              <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
                <CardHeader className="gap-0 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-100/50 p-4">
                  <CardTitle className="flex items-center gap-2 text-lg text-gray-900">
                    <div className="p-1.5 bg-indigo-500 rounded-lg">
                      <GraduationCap className="h-5 w-5 text-white" />
                    </div>
                    Education
                    <Badge variant="outline" className="ml-auto bg-white/80">
                      {employee.educations.length} Records
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {employee.educations.map((edu, index) => (
                      <div key={edu.id} className="group relative p-4 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 rounded-xl border border-blue-200/50 hover:shadow-md hover:shadow-blue-500/10 transition-all duration-300">
                        <div className="absolute top-3 left-3">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                            {index + 1}
                          </div>
                        </div>
                        <div className="ml-9">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-bold text-base text-gray-900 mb-1">{edu.qualification}</h4>
                              <p className="text-blue-700 font-medium text-sm">{edu.instituteName}</p>
                            </div>
                            {edu.certificateUrl && (
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="bg-white/80 hover:bg-white border-blue-200 text-blue-700 hover:text-blue-800 rounded-lg h-8"
                                asChild
                              >
                                <a href={edu.certificateUrl} target="_blank" rel="noopener noreferrer">
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </a>
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Work Experience */}
            {employee.workExperiences && employee.workExperiences.length > 0 && (
              <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
                <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50 border-b border-gray-100/50 p-4">
                  <CardTitle className="flex items-center gap-2 text-lg text-gray-900">
                    <div className="p-1.5 bg-amber-500 rounded-lg">
                      <Briefcase className="h-5 w-5 text-white" />
                    </div>
                    Work Experience
                    <Badge variant="outline" className="ml-auto bg-white/80">
                      {employee.workExperiences.length} Records
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {employee.workExperiences.map((exp, index) => (
                      <div key={exp.id} className="group relative p-4 bg-gradient-to-br from-emerald-50/50 to-teal-50/50 rounded-xl border border-emerald-200/50 hover:shadow-md hover:shadow-emerald-500/10 transition-all duration-300">
                        <div className="absolute top-3 left-3">
                          <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                            {index + 1}
                          </div>
                        </div>
                        <div className="ml-9">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h4 className="font-bold text-base text-gray-900 mb-1">{exp.previousOrganization}</h4>
                              <div className="flex items-center gap-2 text-emerald-700">
                                <Clock className="h-3 w-3" />
                                <span className="font-medium text-sm">{exp.previousExperience}</span>
                              </div>
                            </div>
                            {exp.experienceCertUrl && (
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="bg-white/80 hover:bg-white border-emerald-200 text-emerald-700 hover:text-emerald-800 rounded-lg h-8"
                                asChild
                              >
                                <a href={exp.experienceCertUrl} target="_blank" rel="noopener noreferrer">
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </a>
                              </Button>
                            )}
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="p-2 bg-white/60 rounded-lg">
                              <label className="text-xs font-bold text-emerald-800 uppercase tracking-wide block mb-1">Previous ESI</label>
                              <p className="text-gray-900 font-medium text-sm">{exp.previousESI || 'N/A'}</p>
                            </div>
                            <div className="p-2 bg-white/60 rounded-lg">
                              <label className="text-xs font-bold text-emerald-800 uppercase tracking-wide block mb-1">Previous UAN</label>
                              <p className="text-gray-900 font-medium text-sm">{exp.previousUAN || 'N/A'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Documents */}
            <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
              <CardHeader className="gap-0 bg-gradient-to-r from-yellow-50 to-amber-50 border-b border-gray-100/50 p-4">
                <CardTitle className="flex items-center gap-2 text-base text-gray-900">
                  <div className="p-1.5 bg-yellow-500 rounded-lg">
                    <FileText className="h-4 w-4 text-white" />
                  </div>
                  Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div className="p-3 bg-gradient-to-br from-yellow-50 to-amber-50 rounded-xl border border-yellow-200">
                  <div className="flex justify-between items-start mb-2">
                    <label className="text-xs font-bold text-yellow-800 uppercase tracking-wide">Aadhar Number</label>
                    {employee.aadharCopyUrl && (
                      <Button variant="ghost" size="sm" className="h-5 p-1 text-yellow-700 hover:text-yellow-800" asChild>
                        <a href={employee.aadharCopyUrl} target="_blank" rel="noopener noreferrer">
                          <Eye className="h-3 w-3" />
                        </a>
                      </Button>
                    )}
                  </div>
                  <p className="text-gray-900 font-mono font-bold">{employee.aadhar}</p>
                </div>

                <div className="p-3 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl border border-indigo-200">
                  <div className="flex justify-between items-start mb-2">
                    <label className="text-xs font-bold text-indigo-800 uppercase tracking-wide">PAN Number</label>
                    {employee.panCopyUrl && (
                      <Button variant="ghost" size="sm" className="h-5 p-1 text-indigo-700 hover:text-indigo-800" asChild>
                        <a href={employee.panCopyUrl} target="_blank" rel="noopener noreferrer">
                          <Eye className="h-3 w-3" />
                        </a>
                      </Button>
                    )}
                  </div>
                  <p className="text-gray-900 font-mono font-bold">{employee.pan}</p>
                </div>

                {employee.fatherAadhar && (
                  <div className="p-3 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border border-gray-200">
                    <div className="flex justify-between items-start mb-2">
                      <label className="text-xs font-bold text-gray-800 uppercase tracking-wide">Father Aadhar</label>
                      {employee.fatherAadharCopyUrl && (
                        <Button variant="ghost" size="sm" className="h-5 p-1 text-gray-700 hover:text-gray-800" asChild>
                          <a href={employee.fatherAadharCopyUrl} target="_blank" rel="noopener noreferrer">
                            <Eye className="h-3 w-3" />
                          </a>
                        </Button>
                      )}
                    </div>
                    <p className="text-gray-900 font-mono font-bold text-sm">{employee.fatherAadhar}</p>
                  </div>
                )}

                {employee.motherAadhar && (
                  <div className="p-3 bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl border border-rose-200">
                    <div className="flex justify-between items-start mb-2">
                      <label className="text-xs font-bold text-rose-800 uppercase tracking-wide">Mother Aadhar</label>
                      {employee.motherAadharCopyUrl && (
                        <Button variant="ghost" size="sm" className="h-5 p-1 text-rose-700 hover:text-rose-800" asChild>
                          <a href={employee.motherAadharCopyUrl} target="_blank" rel="noopener noreferrer">
                            <Eye className="h-3 w-3" />
                          </a>
                        </Button>
                      )}
                    </div>
                    <p className="text-gray-900 font-mono font-bold text-sm">{employee.motherAadhar}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
              <CardHeader className="gap-0 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-100/50 p-4">
                <CardTitle className="flex items-center gap-2 text-base text-gray-900">
                  <div className="p-1.5 bg-green-500 rounded-lg">
                    <CreditCard className="h-4 w-4 text-white" />
                  </div>
                  Bank Details
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div className="p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                  <label className="text-xs font-bold text-blue-700 uppercase tracking-wide block mb-1">Bank Name</label>
                  <p className="text-gray-900 font-bold">{employee.bankName || 'N/A'}</p>
                </div>
                
                <div className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                  <label className="text-xs font-bold text-purple-700 uppercase tracking-wide block mb-1">Account Number</label>
                  <p className="text-gray-900 font-mono font-bold">{employee.accountNo || 'N/A'}</p>
                </div>
                
                <div className="grid grid-cols-1 gap-3">
                  <div className="p-3 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl">
                    <label className="text-xs font-bold text-emerald-700 uppercase tracking-wide block mb-1">IFSC Code</label>
                    <p className="text-gray-900 font-mono font-bold">{employee.ifsc || 'N/A'}</p>
                  </div>
                  
                  <div className="p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl">
                    <label className="text-xs font-bold text-orange-700 uppercase tracking-wide block mb-1">Branch</label>
                    <p className="text-gray-900 font-semibold">{employee.branch || 'N/A'}</p>
                  </div>
                </div>
                
                {employee.bankCopyUrl && (
                  <div className="pt-2">
                    <Button 
                      variant="outline" 
                      className="w-full bg-white/80 hover:bg-white border-green-200 text-green-700 hover:text-green-800 rounded-lg group h-8 text-sm"
                      asChild
                    >
                      <a href={employee.bankCopyUrl} target="_blank" rel="noopener noreferrer">
                        <FileText className="h-3 w-3 mr-2 group-hover:scale-110 transition-transform" />
                        View Bank Document
                        <Download className="h-3 w-3 ml-auto opacity-50 group-hover:opacity-100 transition-opacity" />
                      </a>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

                        {/* Family Details */}
            {(employee.fatherName || employee.motherName) && (
              <Card className="p-0 gap-0 bg-white/80 backdrop-blur-md shadow-md border-0 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-500">
                <CardHeader className="gap-0 bg-gradient-to-r from-pink-50 to-rose-50 border-b border-gray-100/50 p-4">
                  <CardTitle className="flex items-center gap-2 text-base text-gray-900">
                    <div className="p-1.5 bg-pink-500 rounded-lg">
                      <Users className="h-4 w-4 text-white" />
                    </div>
                    Family Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 space-y-4">
                  <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <label className="text-xs font-bold text-blue-700 uppercase tracking-wide block mb-1">Father Name</label>
                    <p className="text-gray-900 font-semibold">{employee.fatherName || 'N/A'}</p>
                  </div>
                  <div className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <label className="text-xs font-bold text-purple-700 uppercase tracking-wide block mb-1">Mother Name</label>
                    <p className="text-gray-900 font-semibold">{employee.motherName || 'N/A'}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}