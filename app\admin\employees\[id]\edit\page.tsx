"use client";

import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, ArrowLef<PERSON> } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { employeeSchema } from "@/app/api/employees/schema";
import Link from "next/link";

type EmployeeForm = z.infer<typeof employeeSchema>;

export default function EditEmployeePage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const params = useParams<{ id: string }>()   // ✅ type safety
  
  const form = useForm<EmployeeForm>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      isActive: true,
      educations: [{ instituteName: "", qualification: "" }],
      workExperiences: []
    },
  });

  const { fields: educationFields, append: appendEducation, remove: removeEducation } = useFieldArray({
    control: form.control,
    name: "educations"
  });

  const { fields: workFields, append: appendWork, remove: removeWork } = useFieldArray({
    control: form.control,
    name: "workExperiences"
  });

  useEffect(() => {
    async function fetchEmployee() {
      try {
        const response = await fetch(`/api/employees/${params.id}`);
        const employee = await response.json();
        
        if (employee) {
          form.reset({
            ...employee,
            dob: new Date(employee.dob).toISOString().split('T')[0],
            doj: new Date(employee.doj).toISOString().split('T')[0],
            educations: employee.educations?.length > 0 ? employee.educations : [{ instituteName: "", qualification: "" }],
            workExperiences: employee.workExperiences || []
          });
        }
      } catch (error) {
        console.error('Failed to fetch employee:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchEmployee();
  }, [params.id, form]);

  const uploadFile = async (file: File, fileType: string) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', fileType);
    formData.append('userId', '1');

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('File upload failed');
    }

    const result = await response.json();
    return result.file.storageUrl;
  };

  async function onSubmit(values: EmployeeForm) {
    setIsSubmitting(true);
    try {
      // Handle file uploads
      const formElement = document.querySelector('form') as HTMLFormElement;
      const fileInputs = formElement.querySelectorAll('input[type="file"]') as NodeListOf<HTMLInputElement>;
      
      let profilePicUrl = '';
      let aadharCopyUrl = '';
      let panCopyUrl = '';
      let bankCopyUrl = '';
      let fatherAadharCopyUrl = '';
      let motherAadharCopyUrl = '';

      // Upload files one by one
      for (const input of Array.from(fileInputs)) {
        if (input.files && input.files.length > 0) {
          const file = input.files[0];

          // Try multiple ways to find the label
          let label = '';

          // Method 1: Use data attribute (most reliable)
          label = input.getAttribute('data-file-label') || '';

          // Method 2: Look for label in the same FormItem
          if (!label) {
            const formItem = input.closest('[data-file-type]');
            if (formItem) {
              label = formItem.getAttribute('data-file-type') || '';
            }
          }

          // Method 3: Look for label element
          if (!label) {
            const formItem = input.closest('[class*="FormItem"], .form-item');
            if (formItem) {
              const labelElement = formItem.querySelector('label');
              if (labelElement) {
                label = labelElement.textContent || '';
              }
            }
          }

          console.log('Processing file upload for label:', label);
          console.log('File name:', file.name);

          try {
            const url = await uploadFile(file, 'document');
            console.log('File uploaded successfully:', url);
            
            if (label.includes('Profile Photo')) {
              profilePicUrl = url;
              console.log('Set profilePicUrl:', url);
            } else if (label.includes('Aadhar Copy') && !label.includes('Father') && !label.includes('Mother')) {
              aadharCopyUrl = url;
              console.log('Set aadharCopyUrl:', url);
            } else if (label.includes('PAN Copy')) {
              panCopyUrl = url;
              console.log('Set panCopyUrl:', url);
            } else if (label.includes('Bank') && (label.includes('Copy') || label.includes('Passbook'))) {
              bankCopyUrl = url;
              console.log('Set bankCopyUrl:', url);
            } else if (label.includes("Father") && label.includes("Aadhar")) {
              fatherAadharCopyUrl = url;
              console.log('Set fatherAadharCopyUrl:', url);
            } else if (label.includes("Mother") && label.includes("Aadhar")) {
              motherAadharCopyUrl = url;
              console.log('Set motherAadharCopyUrl:', url);
            } else {
              console.warn('No matching label found for:', label);
            }
          } catch (error) {
            console.error('File upload failed:', error);
          }
        }
      }

      // Add file URLs to form data (only if new files were uploaded)
      const formData = {
        ...values,
        profilePicUrl: profilePicUrl || values.profilePicUrl,
        aadharCopyUrl: aadharCopyUrl || values.aadharCopyUrl,
        panCopyUrl: panCopyUrl || values.panCopyUrl,
        bankCopyUrl: bankCopyUrl || values.bankCopyUrl,
        fatherAadharCopyUrl: fatherAadharCopyUrl || values.fatherAadharCopyUrl,
        motherAadharCopyUrl: motherAadharCopyUrl || values.motherAadharCopyUrl,
      };

      const response = await fetch(`/api/employees/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update employee');
      }

      router.push(`/admin/employees/${params.id}`);
    } catch (error) {
      console.error('Error updating employee:', error);
      alert(error instanceof Error ? error.message : 'Failed to update employee');
    } finally {
      setIsSubmitting(false);
    }
  }

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">Loading employee data...</div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto space-y-8">
      <div className="flex items-center gap-4 mb-8">
        <Link href={`/admin/employees/${params.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Personal Details */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <h2 className="font-bold text-xl text-gray-800 border-b pb-2">Personal Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="employeeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee ID *</FormLabel>
                    <FormControl>
                      <Input placeholder="EMP001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="employeeName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItem data-file-type="Profile Photo">
                <FormLabel>Profile Photo</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*" data-file-label="Profile Photo" />
                </FormControl>
              </FormItem>

              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>D.O.B. *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="9876543210" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bloodGroup"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Blood Group</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select blood group" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="A+">A+</SelectItem>
                        <SelectItem value="A-">A-</SelectItem>
                        <SelectItem value="B+">B+</SelectItem>
                        <SelectItem value="B-">B-</SelectItem>
                        <SelectItem value="AB+">AB+</SelectItem>
                        <SelectItem value="AB-">AB-</SelectItem>
                        <SelectItem value="O+">O+</SelectItem>
                        <SelectItem value="O-">O-</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="maritalStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marital Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select marital status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Single">Single</SelectItem>
                        <SelectItem value="Married">Married</SelectItem>
                        <SelectItem value="Divorced">Divorced</SelectItem>
                        <SelectItem value="Widowed">Widowed</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="IT">IT</SelectItem>
                        <SelectItem value="HR">HR</SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="Marketing">Marketing</SelectItem>
                        <SelectItem value="Operations">Operations</SelectItem>
                        <SelectItem value="Sales">Sales</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="doj"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>D.O.J</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Employee is currently active
                      </div>
                    </div>
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="presentAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Present Address</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="permanentAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Permanent Address</FormLabel>
                    <FormControl>
                      <Textarea {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Identification Details */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <h2 className="font-bold text-xl text-gray-800 border-b pb-2">Identification Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="aadhar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Aadhar Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="1234 5678 9012" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormItem data-file-type="Aadhar Copy">
                <FormLabel>Aadhar Copy</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*,application/pdf" data-file-label="Aadhar Copy" />
                </FormControl>
              </FormItem>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="pan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>PAN *</FormLabel>
                    <FormControl>
                      <Input placeholder="**********" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />              
              <FormItem data-file-type="PAN Copy">
                <FormLabel>PAN Copy</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*,application/pdf" data-file-label="PAN Copy" />
                </FormControl>
              </FormItem>
            </div>
          </div>

          {/* Bank Details */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <h2 className="font-bold text-xl text-gray-800 border-b pb-2">Bank Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="bankName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bank Name</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select bank" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="SBI">State Bank of India</SelectItem>
                        <SelectItem value="HDFC">HDFC Bank</SelectItem>
                        <SelectItem value="ICICI">ICICI Bank</SelectItem>
                        <SelectItem value="Axis">Axis Bank</SelectItem>
                        <SelectItem value="PNB">Punjab National Bank</SelectItem>
                        <SelectItem value="BOI">Bank of India</SelectItem>
                        <SelectItem value="Canara">Canara Bank</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="accountNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account No</FormLabel>
                    <FormControl>
                      <Input placeholder="**********" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="ifsc"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>IFSC</FormLabel>
                    <FormControl>
                      <Input placeholder="SBIN0001234" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branch"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch</FormLabel>
                    <FormControl>
                      <Input placeholder="Main Branch" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormItem data-file-type="Bank Passbook Copy">
                <FormLabel>Bank Passbook Copy</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*,application/pdf" data-file-label="Bank Passbook Copy" />
                </FormControl>
              </FormItem>
            </div>
          </div>

          {/* Educational Details */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <div className="flex justify-between items-center border-b pb-2">
              <h2 className="font-bold text-xl text-gray-800">Educational Details</h2>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendEducation({ instituteName: "", qualification: "" })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Education
              </Button>
            </div>
            {educationFields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold text-gray-700">Education {index + 1}</h3>
                  {educationFields.length > 1 && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeEducation(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name={`educations.${index}.instituteName`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Institute Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="ABC University" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`educations.${index}.qualification`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Educational qualification *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select qualification" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="10th">10th</SelectItem>
                            <SelectItem value="12th">12th</SelectItem>
                            <SelectItem value="Diploma">Diploma</SelectItem>
                            <SelectItem value="B.Tech">B.Tech</SelectItem>
                            <SelectItem value="B.E">B.E</SelectItem>
                            <SelectItem value="BCA">BCA</SelectItem>
                            <SelectItem value="MCA">MCA</SelectItem>
                            <SelectItem value="MBA">MBA</SelectItem>
                            <SelectItem value="M.Tech">M.Tech</SelectItem>
                            <SelectItem value="PhD">PhD</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem>
                    <FormLabel>Attach Certificates</FormLabel>
                    <FormControl>
                      <Input type="file" accept="image/*,application/pdf" multiple />
                    </FormControl>
                  </FormItem>
                </div>
              </div>
            ))}
          </div>

          {/* Work Experience */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <div className="flex justify-between items-center border-b pb-2">
              <h2 className="font-bold text-xl text-gray-800">Work Experience</h2>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendWork({ previousOrganization: "", previousExperience: "", previousESI: "", previousUAN: "" })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Experience
              </Button>
            </div>
            {workFields.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No work experience added. Click &quot;Add Experience&quot; to add one.</p>
            ) : (
              workFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold text-gray-700">Experience {index + 1}</h3>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeWork(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name={`workExperiences.${index}.previousOrganization`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous Organization</FormLabel>
                          <FormControl>
                            <Input placeholder="Company Name" {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`workExperiences.${index}.previousESI`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous ESI No (if any)</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`workExperiences.${index}.previousUAN`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous UAN (if any)</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`workExperiences.${index}.previousExperience`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Previous Experience</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select experience" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="0-1 years">0-1 years</SelectItem>
                              <SelectItem value="1-2 years">1-2 years</SelectItem>
                              <SelectItem value="2-3 years">2-3 years</SelectItem>
                              <SelectItem value="3-5 years">3-5 years</SelectItem>
                              <SelectItem value="5-10 years">5-10 years</SelectItem>
                              <SelectItem value="10+ years">10+ years</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                    <FormItem>
                      <FormLabel>Attach Experience Copy</FormLabel>
                      <FormControl>
                        <Input type="file" accept="image/*,application/pdf" multiple />
                      </FormControl>
                    </FormItem>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Family Details */}
          <div className="p-6 border rounded-xl space-y-6 bg-white shadow-sm">
            <h2 className="font-bold text-xl text-gray-800 border-b pb-2">Family Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="fatherName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Father&apos;s Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="fatherAadhar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Father&apos;s Aadhar</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormItem data-file-type="Father's Aadhar Copy">
                <FormLabel>Father&apos;s Aadhar Copy</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*,application/pdf" data-file-label="Father's Aadhar Copy" />
                </FormControl>
              </FormItem>
              <FormField
                control={form.control}
                name="motherName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mother&apos;s Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="motherAadhar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mother&apos;s Aadhar</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormItem data-file-type="Mother's Aadhar Copy">
                <FormLabel>Mother&apos;s Aadhar Copy</FormLabel>
                <FormControl>
                  <Input type="file" accept="image/*,application/pdf" data-file-label="Mother's Aadhar Copy" />
                </FormControl>
              </FormItem>
            </div>
          </div>

          <div className="flex justify-center gap-4 pt-6">
            <Link href={`/admin/employees/${params.id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              className="px-8 py-3 text-lg font-semibold bg-blue-600 hover:bg-blue-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Updating...' : 'Update Employee'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </div>
  );
}