"use client";

import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, ArrowLeft, Upload, User, Building2, GraduationCap, Briefcase, Users, FileText, CreditCard, Camera } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { employeeSchema } from "@/app/api/employees/schema";
import Link from "next/link";

type EmployeeForm = z.infer<typeof employeeSchema>;

export default function EditEmployeePage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const router = useRouter();
  const params = useParams<{ id: string }>()   // ✅ type safety
  
  const form = useForm<EmployeeForm>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      isActive: true,
      educations: [{ instituteName: "", qualification: "" }],
      workExperiences: []
    },
  });

  const { fields: educationFields, append: appendEducation, remove: removeEducation } = useFieldArray({
    control: form.control,
    name: "educations"
  });

  const { fields: workFields, append: appendWork, remove: removeWork } = useFieldArray({
    control: form.control,
    name: "workExperiences"
  });

  useEffect(() => {
    async function fetchEmployee() {
      try {
        console.log('Fetching employee with ID:', params.id);
        const response = await fetch(`/api/employees/${params.id}`);
        console.log('Fetch response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Failed to fetch employee:', errorText);
          return;
        }

        const employee = await response.json();
        console.log('Fetched employee data:', employee);

        if (employee) {
          const formData = {
            ...employee,
            dob: new Date(employee.dob).toISOString().split('T')[0],
            doj: new Date(employee.doj).toISOString().split('T')[0],
            educations: employee.educations?.length > 0 ? employee.educations : [{ instituteName: "", qualification: "" }],
            workExperiences: employee.workExperiences || []
          };
          console.log('Setting form data:', formData);
          form.reset(formData);
        }
      } catch (error) {
        console.error('Failed to fetch employee:', error);
      } finally {
        setLoading(false);
      }
    }

    if (params.id) {
      fetchEmployee();
    }
  }, [params.id, form]);

  const uploadFile = async (file: File, fileType: string) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', fileType);
    formData.append('userId', '1');

    const response = await fetch('/api/files/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('File upload failed:', errorText);
      throw new Error(`File upload failed: ${errorText}`);
    }

    const result = await response.json();
    console.log('File upload result:', result);
    return result.file?.storageUrl || result.storageUrl;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, label: string) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadingFiles(prev => [...prev, label]);
      // Simulate upload progress - in real app this would be handled in onSubmit
      setTimeout(() => {
        setUploadingFiles(prev => prev.filter(f => f !== label));
      }, 2000);
    }
  };

  async function onSubmit(values: EmployeeForm) {
    console.log('🚀 FORM SUBMISSION STARTED!');
    console.log('Form data being sent:', values);
    console.log('Form validation state:', form.formState.errors);
    console.log('Form is valid:', form.formState.isValid);

    // Show alert to confirm form submission is triggered
    alert('Form submission started! Check console for details.');

    setIsSubmitting(true);
    try {
      // Handle file uploads
      const formElement = document.querySelector('form') as HTMLFormElement;
      const fileInputs = formElement.querySelectorAll('input[type="file"]') as NodeListOf<HTMLInputElement>;
      
      let profilePicUrl = '';
      let aadharCopyUrl = '';
      let panCopyUrl = '';
      let bankCopyUrl = '';
      let fatherAadharCopyUrl = '';
      let motherAadharCopyUrl = '';

      // Upload files one by one
      for (const input of Array.from(fileInputs)) {
        if (input.files && input.files.length > 0) {
          const file = input.files[0];

          // Try multiple ways to find the label
          let label = '';

          // Method 1: Use data attribute (most reliable)
          label = input.getAttribute('data-file-label') || '';

          // Method 2: Look for label in the same FormItem
          if (!label) {
            const formItem = input.closest('[data-file-type]');
            if (formItem) {
              label = formItem.getAttribute('data-file-type') || '';
            }
          }

          // Method 3: Look for label element
          if (!label) {
            const formItem = input.closest('[class*="FormItem"], .form-item');
            if (formItem) {
              const labelElement = formItem.querySelector('label');
              if (labelElement) {
                label = labelElement.textContent || '';
              }
            }
          }

          console.log('Processing file upload for label:', label);
          console.log('File name:', file.name);

          try {
            const url = await uploadFile(file, 'document');
            console.log('File uploaded successfully:', url);
            
            if (label.includes('Profile Photo')) {
              profilePicUrl = url;
              console.log('Set profilePicUrl:', url);
            } else if (label.includes('Aadhar Copy') && !label.includes('Father') && !label.includes('Mother')) {
              aadharCopyUrl = url;
              console.log('Set aadharCopyUrl:', url);
            } else if (label.includes('PAN Copy')) {
              panCopyUrl = url;
              console.log('Set panCopyUrl:', url);
            } else if (label.includes('Bank') && (label.includes('Copy') || label.includes('Passbook'))) {
              bankCopyUrl = url;
              console.log('Set bankCopyUrl:', url);
            } else if (label.includes("Father") && label.includes("Aadhar")) {
              fatherAadharCopyUrl = url;
              console.log('Set fatherAadharCopyUrl:', url);
            } else if (label.includes("Mother") && label.includes("Aadhar")) {
              motherAadharCopyUrl = url;
              console.log('Set motherAadharCopyUrl:', url);
            } else {
              console.warn('No matching label found for:', label);
            }
          } catch (error) {
            console.error('File upload failed:', error);
          }
        }
      }

      // Add file URLs to form data (only if new files were uploaded)
      const formData = {
        ...values,
        profilePicUrl: profilePicUrl || values.profilePicUrl,
        aadharCopyUrl: aadharCopyUrl || values.aadharCopyUrl,
        panCopyUrl: panCopyUrl || values.panCopyUrl,
        bankCopyUrl: bankCopyUrl || values.bankCopyUrl,
        fatherAadharCopyUrl: fatherAadharCopyUrl || values.fatherAadharCopyUrl,
        motherAadharCopyUrl: motherAadharCopyUrl || values.motherAadharCopyUrl,
      };

      const response = await fetch(`/api/employees/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('API Error Response:', error);
        throw new Error(error.error || 'Failed to update employee');
      }

      const result = await response.json();
      console.log('Update successful:', result);
      router.push(`/admin/employees/${params.id}`);
    } catch (error) {
      console.error('Error updating employee:', error);
      if (error instanceof Error) {
        alert(`Update failed: ${error.message}`);
      } else {
        alert('Failed to update employee');
      }
    } finally {
      setIsSubmitting(false);
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <Card className="p-8">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <div className="text-lg font-medium text-slate-700">Loading employee data...</div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href={`/admin/employees/${params.id}`}>
              <Button variant="outline" size="sm" className="shadow-sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Profile
              </Button>
            </Link>
          </div>
          <div className="flex items-center gap-3">
            <div className="p-3 bg-blue-100 rounded-xl">
              <User className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-slate-900">Edit Employee</h1>
              <p className="text-slate-600 mt-1">Update employee information and documents</p>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Personal Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-slate-900">Personal Details</CardTitle>
                    <CardDescription>Basic employee information and contact details</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="employeeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Employee ID *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="EMP001" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="employeeName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Employee Name *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="John Doe" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Email *</FormLabel>
                        <FormControl>
                          <Input 
                            type="email" 
                            placeholder="<EMAIL>" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormItem data-file-type="Profile Photo">
                    <FormLabel className="text-sm font-semibold text-slate-700">Profile Photo</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="file" 
                          accept="image/*" 
                          data-file-label="Profile Photo"
                          onChange={(e) => handleFileChange(e, "Profile Photo")}
                          className="h-11 border-slate-200 focus:border-blue-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                        <Camera className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                        {uploadingFiles.includes("Profile Photo") && (
                          <div className="absolute inset-0 bg-blue-50/80 rounded-md flex items-center justify-center">
                            <div className="flex items-center gap-2 text-sm text-blue-600">
                              <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                              Uploading...
                            </div>
                          </div>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>

                  <FormField
                    control={form.control}
                    name="dob"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Date of Birth *</FormLabel>
                        <FormControl>
                          <Input 
                            type="date" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="mobileNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Mobile Number *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="9876543210" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bloodGroup"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Blood Group</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select blood group" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="A+">A+</SelectItem>
                            <SelectItem value="A-">A-</SelectItem>
                            <SelectItem value="B+">B+</SelectItem>
                            <SelectItem value="B-">B-</SelectItem>
                            <SelectItem value="AB+">AB+</SelectItem>
                            <SelectItem value="AB-">AB-</SelectItem>
                            <SelectItem value="O+">O+</SelectItem>
                            <SelectItem value="O-">O-</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maritalStatus"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Marital Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select marital status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Single">Single</SelectItem>
                            <SelectItem value="Married">Married</SelectItem>
                            <SelectItem value="Divorced">Divorced</SelectItem>
                            <SelectItem value="Widowed">Widowed</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Department</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="IT">IT</SelectItem>
                            <SelectItem value="HR">HR</SelectItem>
                            <SelectItem value="Finance">Finance</SelectItem>
                            <SelectItem value="Marketing">Marketing</SelectItem>
                            <SelectItem value="Operations">Operations</SelectItem>
                            <SelectItem value="Sales">Sales</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="doj"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Date of Joining</FormLabel>
                        <FormControl>
                          <Input 
                            type="date" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="col-span-full">
                        <div className="flex items-center space-x-3 p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-5 w-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                            />
                          </FormControl>
                          <div className="flex-1">
                            <FormLabel className="text-base font-medium text-slate-900">Active Status</FormLabel>
                            <div className="text-sm text-slate-600">
                              Employee is currently active and can access the system
                            </div>
                          </div>
                          <Badge variant={field.value ? "default" : "secondary"} className="ml-auto">
                            {field.value ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                <Separator className="my-8" />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="presentAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Present Address</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            rows={4}
                            className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                            placeholder="Enter current address..."
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="permanentAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Permanent Address</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            rows={4}
                            className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                            placeholder="Enter permanent address..."
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Identification Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-t-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-amber-100 rounded-lg">
                    <FileText className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-slate-900">Identification Details</CardTitle>
                    <CardDescription>Government issued identification documents</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="aadhar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Aadhar Number *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="1234 5678 9012" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem data-file-type="Aadhar Copy" className="md:col-span-2">
                    <FormLabel className="text-sm font-semibold text-slate-700">Aadhar Copy</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="file" 
                          accept="image/*,application/pdf" 
                          data-file-label="Aadhar Copy"
                          onChange={(e) => handleFileChange(e, "Aadhar Copy")}
                          className="h-11 border-slate-200 focus:border-blue-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-amber-50 file:text-amber-700 hover:file:bg-amber-100"
                        />
                        <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                        {uploadingFiles.includes("Aadhar Copy") && (
                          <div className="absolute inset-0 bg-amber-50/80 rounded-md flex items-center justify-center">
                            <div className="flex items-center gap-2 text-sm text-amber-600">
                              <div className="animate-spin h-4 w-4 border-2 border-amber-600 border-t-transparent rounded-full"></div>
                              Uploading...
                            </div>
                          </div>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="pan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">PAN *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="**********" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem data-file-type="PAN Copy" className="md:col-span-2">
                    <FormLabel className="text-sm font-semibold text-slate-700">PAN Copy</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="file" 
                          accept="image/*,application/pdf" 
                          data-file-label="PAN Copy"
                          onChange={(e) => handleFileChange(e, "PAN Copy")}
                          className="h-11 border-slate-200 focus:border-blue-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-amber-50 file:text-amber-700 hover:file:bg-amber-100"
                        />
                        <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                        {uploadingFiles.includes("PAN Copy") && (
                          <div className="absolute inset-0 bg-amber-50/80 rounded-md flex items-center justify-center">
                            <div className="flex items-center gap-2 text-sm text-amber-600">
                              <div className="animate-spin h-4 w-4 border-2 border-amber-600 border-t-transparent rounded-full"></div>
                              Uploading...
                            </div>
                          </div>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                </div>
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CreditCard className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-slate-900">Bank Details</CardTitle>
                    <CardDescription>Banking information for salary processing</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Bank Name</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select bank" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SBI">State Bank of India</SelectItem>
                            <SelectItem value="HDFC">HDFC Bank</SelectItem>
                            <SelectItem value="ICICI">ICICI Bank</SelectItem>
                            <SelectItem value="Axis">Axis Bank</SelectItem>
                            <SelectItem value="PNB">Punjab National Bank</SelectItem>
                            <SelectItem value="BOI">Bank of India</SelectItem>
                            <SelectItem value="Canara">Canara Bank</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="accountNo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Account Number</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="**********" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="ifsc"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">IFSC Code</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="SBIN0001234" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="branch"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Branch Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Main Branch" 
                            {...field} 
                            className="h-11 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormItem data-file-type="Bank Passbook Copy" className="md:col-span-2">
                    <FormLabel className="text-sm font-semibold text-slate-700">Bank Passbook Copy</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="file" 
                          accept="image/*,application/pdf" 
                          data-file-label="Bank Passbook Copy"
                          onChange={(e) => handleFileChange(e, "Bank Passbook Copy")}
                          className="h-11 border-slate-200 focus:border-blue-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                        />
                        <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                        {uploadingFiles.includes("Bank Passbook Copy") && (
                          <div className="absolute inset-0 bg-green-50/80 rounded-md flex items-center justify-center">
                            <div className="flex items-center gap-2 text-sm text-green-600">
                              <div className="animate-spin h-4 w-4 border-2 border-green-600 border-t-transparent rounded-full"></div>
                              Uploading...
                            </div>
                          </div>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                </div>
              </CardContent>
            </Card>

            {/* Educational Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <GraduationCap className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-slate-900">Educational Details</CardTitle>
                      <CardDescription>Academic qualifications and certifications</CardDescription>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendEducation({ instituteName: "", qualification: "" })}
                    className="bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Education
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                {educationFields.map((field, index) => (
                  <div key={field.id} className="border border-slate-200 rounded-xl p-6 bg-slate-50/50">
                    <div className="flex justify-between items-center mb-6">
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                          Education {index + 1}
                        </Badge>
                      </div>
                      {educationFields.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeEducation(index)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name={`educations.${index}.instituteName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Institute Name *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="ABC University" 
                                {...field} 
                                className="h-11 border-slate-200 focus:border-purple-500 focus:ring-purple-500/20"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`educations.${index}.qualification`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Educational Qualification *</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-11 border-slate-200 focus:border-purple-500 focus:ring-purple-500/20">
                                  <SelectValue placeholder="Select qualification" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="10th">10th</SelectItem>
                                <SelectItem value="12th">12th</SelectItem>
                                <SelectItem value="Diploma">Diploma</SelectItem>
                                <SelectItem value="B.Tech">B.Tech</SelectItem>
                                <SelectItem value="B.E">B.E</SelectItem>
                                <SelectItem value="BCA">BCA</SelectItem>
                                <SelectItem value="MCA">MCA</SelectItem>
                                <SelectItem value="MBA">MBA</SelectItem>
                                <SelectItem value="M.Tech">M.Tech</SelectItem>
                                <SelectItem value="PhD">PhD</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-slate-700">Certificates</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type="file" 
                              accept="image/*,application/pdf" 
                              multiple
                              className="h-11 border-slate-200 focus:border-purple-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                            />
                            <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                          </div>
                        </FormControl>
                      </FormItem>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Work Experience */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-teal-100 rounded-lg">
                      <Briefcase className="h-5 w-5 text-teal-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-slate-900">Work Experience</CardTitle>
                      <CardDescription>Previous employment history and experience</CardDescription>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => appendWork({ previousOrganization: "", previousExperience: "", previousESI: "", previousUAN: "" })}
                    className="bg-teal-50 hover:bg-teal-100 border-teal-200 text-teal-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Experience
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-8 space-y-6">
                {workFields.length === 0 ? (
                  <div className="text-center py-12 bg-slate-50/50 rounded-xl border border-dashed border-slate-300">
                    <Briefcase className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-600 font-medium mb-2">No work experience added yet</p>
                    <p className="text-slate-500 text-sm">Click Add Experience to add previous work experience</p>
                  </div>
                ) : (
                  workFields.map((field, index) => (
                    <div key={field.id} className="border border-slate-200 rounded-xl p-6 bg-slate-50/50">
                      <div className="flex justify-between items-center mb-6">
                        <div className="flex items-center gap-3">
                          <Badge variant="secondary" className="bg-teal-100 text-teal-700">
                            Experience {index + 1}
                          </Badge>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeWork(index)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousOrganization`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">Previous Organization</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Company Name" 
                                  {...field} 
                                  className="h-11 border-slate-200 focus:border-teal-500 focus:ring-teal-500/20"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousESI`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">Previous ESI No</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="ESI Number (if any)"
                                  {...field} 
                                  className="h-11 border-slate-200 focus:border-teal-500 focus:ring-teal-500/20"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousUAN`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">Previous UAN</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="UAN Number (if any)"
                                  {...field} 
                                  className="h-11 border-slate-200 focus:border-teal-500 focus:ring-teal-500/20"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`workExperiences.${index}.previousExperience`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-semibold text-slate-700">Experience Duration</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger className="h-11 border-slate-200 focus:border-teal-500 focus:ring-teal-500/20">
                                    <SelectValue placeholder="Select experience" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="0-1 years">0-1 years</SelectItem>
                                  <SelectItem value="1-2 years">1-2 years</SelectItem>
                                  <SelectItem value="2-3 years">2-3 years</SelectItem>
                                  <SelectItem value="3-5 years">3-5 years</SelectItem>
                                  <SelectItem value="5-10 years">5-10 years</SelectItem>
                                  <SelectItem value="10+ years">10+ years</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormItem>
                          )}
                        />
                        <FormItem className="md:col-span-2">
                          <FormLabel className="text-sm font-semibold text-slate-700">Experience Certificate</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input 
                                type="file" 
                                accept="image/*,application/pdf" 
                                multiple
                                className="h-11 border-slate-200 focus:border-teal-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100"
                              />
                              <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                            </div>
                          </FormControl>
                        </FormItem>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Family Details */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-pink-50 to-rose-50 rounded-t-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-pink-100 rounded-lg">
                    <Users className="h-5 w-5 text-pink-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-slate-900">Family Details</CardTitle>
                    <CardDescription>Family member information and documentation</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-8">
                  {/* Father's Details */}
                  <div className="bg-slate-50/50 rounded-xl p-6 border border-slate-200">
                    <h3 className="font-semibold text-lg text-slate-800 mb-6 flex items-center gap-2">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      Father Details
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="fatherName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Father Name</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter father name"
                                {...field} 
                                className="h-11 border-slate-200 focus:border-pink-500 focus:ring-pink-500/20"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="fatherAadhar"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Father Aadhar Number</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="1234 5678 9012"
                                {...field} 
                                className="h-11 border-slate-200 focus:border-pink-500 focus:ring-pink-500/20"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormItem data-file-type="Father Aadhar Copy">
                        <FormLabel className="text-sm font-semibold text-slate-700">Father Aadhar Copy</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type="file" 
                              accept="image/*,application/pdf" 
                              data-file-label="Father's Aadhar Copy"
                              onChange={(e) => handleFileChange(e, "Father Aadhar Copy")}
                              className="h-11 border-slate-200 focus:border-pink-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-pink-50 file:text-pink-700 hover:file:bg-pink-100"
                            />
                            <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                            {uploadingFiles.includes("Father's Aadhar Copy") && (
                              <div className="absolute inset-0 bg-pink-50/80 rounded-md flex items-center justify-center">
                                <div className="flex items-center gap-2 text-sm text-pink-600">
                                  <div className="animate-spin h-4 w-4 border-2 border-pink-600 border-t-transparent rounded-full"></div>
                                  Uploading...
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                      </FormItem>
                    </div>
                  </div>

                  {/* Mother Details */}
                  <div className="bg-slate-50/50 rounded-xl p-6 border border-slate-200">
                    <h3 className="font-semibold text-lg text-slate-800 mb-6 flex items-center gap-2">
                      <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                      Mother Details
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="motherName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Mother Name</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter mother's name"
                                {...field} 
                                className="h-11 border-slate-200 focus:border-pink-500 focus:ring-pink-500/20"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="motherAadhar"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-semibold text-slate-700">Mother Aadhar Number</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="1234 5678 9012"
                                {...field} 
                                className="h-11 border-slate-200 focus:border-pink-500 focus:ring-pink-500/20"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormItem data-file-type="Mother Aadhar Copy">
                        <FormLabel className="text-sm font-semibold text-slate-700">Mother Aadhar Copy</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type="file" 
                              accept="image/*,application/pdf" 
                              data-file-label="Mother's Aadhar Copy"
                              onChange={(e) => handleFileChange(e, "Mother's Aadhar Copy")}
                              className="h-11 border-slate-200 focus:border-pink-500 pt-2.5 file:mr-4 file:py-1 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-pink-50 file:text-pink-700 hover:file:bg-pink-100"
                            />
                            <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                            {uploadingFiles.includes("Mother's Aadhar Copy") && (
                              <div className="absolute inset-0 bg-pink-50/80 rounded-md flex items-center justify-center">
                                <div className="flex items-center gap-2 text-sm text-pink-600">
                                  <div className="animate-spin h-4 w-4 border-2 border-pink-600 border-t-transparent rounded-full"></div>
                                  Uploading...
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                      </FormItem>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Debug Section */}
            <Card className="shadow-lg border-0 bg-red-50 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="text-sm">
                  <p><strong>Form Valid:</strong> {form.formState.isValid ? '✅ Yes' : '❌ No'}</p>
                  <p><strong>Form Errors:</strong> {Object.keys(form.formState.errors).length}</p>
                  <div className="flex gap-2 mt-2">
                    <button
                      type="button"
                      onClick={() => {
                        console.log('Form state:', form.formState);
                        console.log('Form errors:', form.formState.errors);
                        console.log('Form values:', form.getValues());
                      }}
                      className="px-4 py-2 bg-blue-500 text-white rounded"
                    >
                      Debug Form State
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🧪 MANUAL FORM SUBMISSION TEST');
                        const values = form.getValues();
                        onSubmit(values);
                      }}
                      className="px-4 py-2 bg-green-500 text-white rounded"
                    >
                      Test Submit
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="flex justify-center gap-6">
                  <Link href={`/admin/employees/${params.id}`}>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="lg"
                      className="px-8 py-4 h-auto text-base font-medium border-slate-300 hover:bg-slate-50"
                    >
                      Cancel Changes
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    size="lg"
                    className="px-12 py-4 h-auto text-base font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
                    disabled={isSubmitting}
                    onClick={() => console.log('Update button clicked!')}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-3">
                        <div className="animate-spin h-5 w-5 border-2 border-white/30 border-t-white rounded-full"></div>
                        Updating Employee...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Building2 className="h-5 w-5" />
                        Update Employee
                      </div>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>
    </div>
  );
}