import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma"
import { employeeSchema } from "../schema";
import { authenticateUser, canAccessEmployees } from "@/lib/auth";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        // TEMPORARY: Skip authentication for testing
        console.log('🔧 TESTING MODE: Authentication temporarily disabled');

        // Authenticate user
        // const authResult = await authenticateUser(request);
        // if (!authResult.success || !authResult.user) {
        //     return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        // }

        // Check permissions
        // if (!canAccessEmployees(authResult.user, 'READ')) {
        //     return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
        // }

        const { id } = await params;
        const employee = await prisma.employee.findUnique({
            where: { id },
            include: {
                educations: true,
                workExperiences: true
            }
        });

        if (!employee) {
            return NextResponse.json({ error: "Employee not found" }, { status: 404 });
        }

        return NextResponse.json(employee);
    } catch (error) {
        console.error("Get employee error:", error);
        return NextResponse.json({ error: "Failed to fetch employee" }, { status: 500 });
    }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        // TEMPORARY: Skip authentication for testing
        console.log('🔧 TESTING MODE: Authentication temporarily disabled');

        // Authenticate user
        // const authResult = await authenticateUser(request);
        // if (!authResult.success || !authResult.user) {
        //     return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        // }

        // Check permissions
        // if (!canAccessEmployees(authResult.user, 'UPDATE')) {
        //     return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
        // }

        const { id } = await params;
        const body = await request.json();
        console.log('Update request body:', JSON.stringify(body, null, 2));

        const validation = employeeSchema.safeParse(body);

        if (!validation.success) {
            console.error('Validation failed:', validation.error.issues);
            return NextResponse.json({ error: "Validation failed", details: validation.error.issues }, { status: 400 });
        }

        const { educations, workExperiences, ...employeeData } = body;

        const updatedEmployee = await prisma.employee.update({
            where: { id },
            data: {
                ...employeeData,
                dob: new Date(employeeData.dob),
                doj: new Date(employeeData.doj),
                educations: {
                    deleteMany: {},
                    create: educations
                },
                workExperiences: workExperiences ? {
                    deleteMany: {},
                    create: workExperiences
                } : undefined
            },
            include: {
                educations: true,
                workExperiences: true
            }
        });

        return NextResponse.json(updatedEmployee);
    } catch (error) {
        console.error("Update employee error:", error);
        return NextResponse.json({ error: "Failed to update employee" }, { status: 500 });
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        // Authenticate user
        const authResult = await authenticateUser(request);
        if (!authResult.success || !authResult.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Check permissions - only ADMIN can delete employees
        if (!canAccessEmployees(authResult.user, 'DELETE')) {
            return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
        }

        const { id } = await params;
        await prisma.employee.delete({
            where: { id }
        });

        return NextResponse.json({ message: "Employee deleted successfully" });
    } catch (error) {
        console.error("Delete employee error:", error);
        return NextResponse.json({ error: "Failed to delete employee" }, { status: 500 });
    }
}