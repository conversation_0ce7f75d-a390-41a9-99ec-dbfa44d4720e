import { NextRequest, NextResponse } from 'next/server';
import { uploadToSupabase } from '@/lib/utils/fileUpload';
import { authenticateUser, canAccessFiles } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (!canAccessFiles(authResult.user, 'UPLOAD')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Upload to Supabase Storage
    const bucket = 'ctq-files'; // Use existing bucket
    const folder = 'employee-documents';
    
    const { url } = await uploadToSupabase(file, bucket, folder);

    return NextResponse.json({
      message: 'File uploaded successfully',
      file: { storageUrl: url },
    });

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'File upload failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}