import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma"
import { employeeSchema } from "./schema";

export async function GET() {
    try {
        const employees = await prisma.employee.findMany({
            include: {
                educations: true,
                workExperiences: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        return NextResponse.json(employees, { status: 200 });
    } catch (error) {
        console.error("Get employees error:", error);
        return NextResponse.json({ error: "Failed to fetch employees" }, { status: 500 });
    }
}

export async function POST(request:NextRequest){
    try{
        const body = await request.json();
        console.log('Received employee data:', JSON.stringify(body, null, 2));

        const validation = employeeSchema.safeParse(body);
        if(!validation.success){
            console.log('Validation errors:', validation.error.format());
            return NextResponse.json(validation.error.format(),{status:400});
        }
        const existing = await prisma.employee.findUnique({
            where:{employeeId:body.employeeId}
        });
        if(existing){
            return NextResponse.json({error:"Employee ID already exists"},{status:400});
        }

        const { educations, workExperiences, ...employeeData } = body;

        console.log('Employee data for DB:', JSON.stringify(employeeData, null, 2));
        console.log('Educations:', JSON.stringify(educations, null, 2));
        console.log('Work experiences:', JSON.stringify(workExperiences, null, 2));

        const newEmployee = await prisma.employee.create({
            data: {
                ...employeeData,
                dob: new Date(employeeData.dob),
                doj: new Date(employeeData.doj),
                educations: {
                    create: educations
                },
                workExperiences: workExperiences && workExperiences.length > 0 ? {
                    create: workExperiences
                } : undefined
            },
            include: {
                educations: true,
                workExperiences: true
            }
        });

        console.log('Created employee:', JSON.stringify(newEmployee, null, 2));
        return NextResponse.json(newEmployee , {status:201});
    } catch (error){
        console.error("Employee creation error:", error);
        return NextResponse.json({ error: "Internal Server Error", details: error instanceof Error ? error.message : String(error) }, { status: 500 });
    }
}