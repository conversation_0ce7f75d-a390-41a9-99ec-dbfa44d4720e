import React from 'react'

// Skeleton Component
interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
}

const Skeleton: React.FC<SkeletonProps> = ({ className = "", ...props }) => (
  <div 
    className={`animate-pulse rounded-md bg-gray-200 dark:bg-gray-700 ${className}`} 
    {...props} 
  />
)

// Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline'
  size?: 'default' | 'sm' | 'lg'
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  variant = "default", 
  size = "default", 
  className = "", 
  ...props 
}) => {
  const baseClasses = "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400",
    outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-700"
  }
  const sizes = {
    default: "h-10 px-4 py-2",
    sm: "h-9 px-3",
    lg: "h-11 px-8"
  }
  
  return (
    <button 
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

// Input Component
type InputProps = React.InputHTMLAttributes<HTMLInputElement>

const Input: React.FC<InputProps> = ({ className = "", ...props }) => (
  <input
    className={`flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
    {...props}
  />
)

// Loading Table Row Interface
interface TableRowSkeletonProps {
  index: number
}

const TableRowSkeleton: React.FC<TableRowSkeletonProps> = ({ index }) => (
  <tr className="border-b border-gray-100 hover:bg-gray-50">
    <td className="p-4 align-middle">
      <div className="flex items-center space-x-3">
        <Skeleton className="h-8 w-8 rounded-full" />
        <Skeleton className="h-4 w-32" />
      </div>
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-4 w-20" />
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-4 w-40" />
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-6 w-16 rounded-full" />
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-4 w-24" />
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-4 w-28" />
    </td>
    <td className="p-4 align-middle">
      <Skeleton className="h-4 w-20" />
    </td>
    <td className="p-4 align-middle text-right">
      <div className="flex items-center justify-end space-x-2">
        <Skeleton className="h-8 w-8 rounded" />
        <Skeleton className="h-8 w-8 rounded" />
      </div>
    </td>
  </tr>
)

// Main Loading Table Component
const LoadingTable: React.FC = () => {
  const skeletonRows: number[] = Array.from({ length: 10 }, (_, i) => i)

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header with Search and Add Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search employees..."
            className="max-w-sm"
            disabled
          />
        </div>
        <Button disabled>
          Add Employee
        </Button>
      </div>

      {/* Loading Table */}
      <div className="rounded-md border border-gray-200 overflow-hidden bg-white">
        <div className="w-full overflow-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b border-gray-200 bg-gray-50">
              <tr>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-24" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-32" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-16" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-24" />
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-gray-700">
                  <Skeleton className="h-4 w-20" />
                </th>
                <th className="h-12 px-4 text-right align-middle font-medium text-gray-700">
                  <div className="flex justify-end">
                    <Skeleton className="h-4 w-16" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              {skeletonRows.map((index) => (
                <TableRowSkeleton key={index} index={index} />
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Loading Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoadingTable