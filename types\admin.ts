// Admin Interfaces
export enum Role {
  ADMIN = "ADMIN",
  MANAGER = "MANAGER", 
  EMPLOYEE = "EMPLOYEE"
}

export type Employee = {
  id: string
  employeeId: string
  employeeName: string
  email?: string
  isActive: boolean
  dob: Date | string
  presentAddress?: string
  permanentAddress?: string
  mobileNumber: string
  aadhar: string
  pan: string
  bankName?: string
  accountNo?: string
  ifsc?: string
  branch?: string
  bloodGroup?: string
  maritalStatus?: string
  department?: string
  doj: Date | string
  fatherName?: string
  fatherAadhar?: string
  motherName?: string
  motherAadhar?: string
  profilePicUrl?: string
  aadharCopyUrl?: string
  panCopyUrl?: string
  bankCopyUrl?: string
  fatherAadharCopyUrl?: string
  motherAadharCopyUrl?: string
  expCertificateUrl?: string
  educations?: Education[]
  workExperiences?: WorkExperience[]
  createdAt: Date | string
  updatedAt: Date | string
}

export type Education = {
  id: string
  instituteName: string
  qualification: string
  certificateUrl?: string
  employeeId: string
  createdAt: Date | string
  updatedAt: Date | string
}

export type WorkExperience = {
  id: string
  previousOrganization: string
  previousExperience: string
  previousESI?: string
  previousUAN?: string
  experienceCertUrl?: string
  employeeId: string
  createdAt: Date | string
  updatedAt: Date | string
}