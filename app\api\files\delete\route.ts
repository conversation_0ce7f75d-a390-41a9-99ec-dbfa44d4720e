import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { fileDeleteSchema } from '../schema';
import { deleteFromSupabase } from '@/lib/utils/fileUpload';
import { authenticateUser, canAccessFiles } from '@/lib/auth';

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate input
    const validationResult = fileDeleteSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error },
        { status: 400 }
      );
    }

    const { fileId, userId } = validationResult.data;

    // Find file and verify ownership
    const file = await prisma.file.findFirst({
      where: {
        id: fileId,
        userId,
      },
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or unauthorized' },
        { status: 404 }
      );
    }

    // Check permissions - users can only delete their own files unless they're admin/HR
    if (!canAccessFiles(authResult.user, 'DELETE', file.userId.toString())) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Delete from Supabase Storage
    const bucket = 'ctq-users';
    await deleteFromSupabase(bucket, file.fileName);

    // Delete from database
    await prisma.file.delete({
      where: { id: fileId },
    });

    return NextResponse.json({
      message: 'File deleted successfully',
    });

  } catch (error) {
    console.error('File delete error:', error);
    return NextResponse.json(
      { error: 'File deletion failed' },
      { status: 500 }
    );
  }
}