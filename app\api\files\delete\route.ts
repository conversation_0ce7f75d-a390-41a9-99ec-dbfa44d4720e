import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { fileDeleteSchema } from '../schema';
import { deleteFromSupabase } from '@/lib/utils/fileUpload';

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validationResult = fileDeleteSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error },
        { status: 400 }
      );
    }

    const { fileId, userId } = validationResult.data;

    // Find file and verify ownership
    const file = await prisma.file.findFirst({
      where: {
        id: fileId,
        userId,
      },
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or unauthorized' },
        { status: 404 }
      );
    }

    // Delete from Supabase Storage
    const bucket = 'ctq-users';
    await deleteFromSupabase(bucket, file.fileName);

    // Delete from database
    await prisma.file.delete({
      where: { id: fileId },
    });

    return NextResponse.json({
      message: 'File deleted successfully',
    });

  } catch (error) {
    console.error('File delete error:', error);
    return NextResponse.json(
      { error: 'File deletion failed' },
      { status: 500 }
    );
  }
}